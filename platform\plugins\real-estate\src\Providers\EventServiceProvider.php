<?php

namespace Xmetr\RealEstate\Providers;

use Xmetr\Base\Events\UpdatedContentEvent;
use Xmetr\Media\Events\MediaFileUploaded;
use Xmetr\Payment\Events\PaymentWebhookReceived;
use Xmetr\RealEstate\Events\PaymentCompleted;
use Xmetr\RealEstate\Listeners\AddSitemapListener;
use Xmetr\RealEstate\Listeners\GenerateVideoThumbnailListener;
use Xmetr\RealEstate\Listeners\PageSitemapFilterListener;
use Xmetr\RealEstate\Listeners\SubscribedPackageListener;
use Xmetr\RealEstate\Listeners\UpdatedContentListener;
use Xmetr\Theme\Events\RenderingSiteMapEvent;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        UpdatedContentEvent::class => [
            UpdatedContentListener::class,
        ],
        RenderingSiteMapEvent::class => [
            AddSitemapListener::class,
            PageSitemapFilterListener::class,
        ],
        PaymentWebhookReceived::class => [
            SubscribedPackageListener::class,
        ],
        PaymentCompleted::class => [
            SubscribedPackageListener::class,
        ],
        MediaFileUploaded::class => [
            // GenerateVideoThumbnailListener::class,
        ],
    ];
}
