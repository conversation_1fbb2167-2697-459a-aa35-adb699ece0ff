@php
    $itemLayout ??= request()->input('layout', 'grid');
    $itemLayout = in_array($itemLayout, ['grid', 'list']) ? $itemLayout : 'grid';
    $layout ??= get_property_listing_page_layout();

    if (! isset($itemsPerRow)) {
        $itemsPerRow = $itemLayout === 'grid' ? 3 : 2;
        if (! in_array($layout, ['top-map', 'without-map'])) {
            $itemsPerRow = $itemLayout === 'grid' ? 2 : 1;
        }
    }
@endphp

@if ($projects->isNotEmpty())
<input type="hidden" id="found-filter-page-title" value="@isset($page_title) {{ $page_title }} @endisset">
<input type="hidden" id="found-listings" value="{{ $projects instanceof \Illuminate\Pagination\LengthAwarePaginator ? $projects->total() : $projects->count() }}">
    @include(Theme::getThemeNamespace("views.real-estate.projects.$itemLayout"), compact('itemsPerRow'))
@else
    @php
        SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
    @endphp
    <input type="hidden" id="found-filter-page-title" value="{{ __('No projects found.') }}">
    <div class="alert alert-warning" role="alert" id="no-projects-found-filter-page-title">
        {{ __('No projects found.') }}
    </div>
@endif

@if ($projects instanceof \Illuminate\Pagination\LengthAwarePaginator && $projects->hasPages())
    <div class="justify-content-center wd-navigation">
        {{ $projects->withQueryString()->links(Theme::getThemeNamespace('partials.pagination')) }}
    </div>
@endif
