<?php

namespace Xmetr\RealEstate\Http\Controllers\Fronts;

use Xmetr\Base\Facades\Assets;
use Xmetr\Base\Facades\EmailHandler;
use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Language\Facades\Language;
use Xmetr\Media\Chunks\Exceptions\UploadMissingFileException;
use Xmetr\Media\Chunks\Handler\DropZoneUploadHandler;
use Xmetr\Media\Chunks\Receiver\FileReceiver;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\Media\Models\MediaFile;
use Xmetr\Optimize\Facades\OptimizerHelper;
use Xmetr\Payment\Enums\PaymentStatusEnum;
use Xmetr\Payment\Models\Payment;
use Xmetr\PayPal\Services\Gateways\PayPalPaymentService;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\AccountForm;
use Xmetr\RealEstate\Forms\Fronts\ChangePasswordForm;
use Xmetr\RealEstate\Forms\Fronts\ProfileForm;
use Xmetr\RealEstate\Http\Requests\AvatarRequest;
use Xmetr\RealEstate\Http\Requests\SettingRequest;
use Xmetr\RealEstate\Http\Requests\UpdatePasswordRequest;
use Xmetr\RealEstate\Http\Resources\AccountResource;
use Xmetr\RealEstate\Http\Resources\ActivityLogResource;
use Xmetr\RealEstate\Http\Resources\PackageResource;
use Xmetr\RealEstate\Http\Resources\TransactionResource;
use Xmetr\RealEstate\Models\Account;
use Xmetr\RealEstate\Models\AccountActivityLog;
use Xmetr\RealEstate\Models\Package;
use Xmetr\RealEstate\Models\Transaction;
use Xmetr\RealEstate\Services\CouponService;
use Xmetr\RealEstate\Services\VideoThumbnailService;
use Xmetr\Theme\Facades\Theme;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PublicAccountController extends BaseController
{
    public function __construct()
    {
        OptimizerHelper::disable();
    }

    public function getDashboard()
    {
        $user = auth('account')->user();

        $this->pageTitle($user->name);

        Assets::usingVueJS()
            ->addScriptsDirectly('vendor/core/plugins/real-estate/js/components.js');

        return view('plugins/real-estate::themes.dashboard.index', compact('user'));
    }

    public function getSettings()
    {
        $this->pageTitle(trans('plugins/real-estate::account.account_settings'));

        $user = auth('account')->user();

        Assets::addScriptsDirectly('vendor/core/plugins/location/js/location.js');

        $profileForm = ProfileForm::createFromModel($user)
            ->renderForm();

        $changePasswordForm = ChangePasswordForm::create()
            ->renderForm();

        return view(
            $this->getViewFileName('dashboard.settings.index'),
            compact('user', 'profileForm', 'changePasswordForm')
        );
    }

    protected function getViewFileName(string $view): string
    {
        if (view()->exists($themeView = Theme::getThemeNamespace('views.real-estate.' . $view))) {
            return $themeView;
        }

        return 'plugins/real-estate::themes.' . $view;
    }

    public function postSettings(SettingRequest $request)
    {
        /**
         * @var Account $account
         */
        $account = auth('account')->user();

        $form = ProfileForm::createFromModel($account)->setRequest($request);

        $form
            ->saving(function (AccountForm $form) use ($request): void {
                $account = $form->getModel();

                $account->fill($request->except(['password', 'email']));

                $account->dob = Carbon::parse($request->input('dob'))->toDateString();
                $account->save();

                // Sync spoken languages
                $spokenLanguages = $request->input('spoken_languages', []);

                // Ensure we have a proper array and filter out invalid values
                if (is_array($spokenLanguages)) {
                    $spokenLanguages = array_filter($spokenLanguages, function($value) {
                        return !empty($value) && is_numeric($value) && $value > 0;
                    });
                    // Re-index the array to avoid issues with sync
                    $spokenLanguages = array_values($spokenLanguages);
                } else {
                    $spokenLanguages = [];
                }

                $account->spokenLanguages()->sync($spokenLanguages);

                do_action('update_account_settings', $account);

                AccountActivityLog::query()->create(['action' => 'update_setting']);
            });

        return $this
            ->httpResponse()
            ->setNextUrl(route('public.account.settings'))
            ->setMessage(trans('plugins/real-estate::account.update_profile_success'));
    }

    public function getPackages()
    {
        abort_unless(RealEstateHelper::isEnabledCreditsSystem(), 404);

        $this->pageTitle(trans('plugins/real-estate::account.packages'));

        Assets::addScriptsDirectly('vendor/core/plugins/real-estate/js/components.js');

        Assets::usingVueJS();

        return view($this->getViewFileName('dashboard.settings.package'));
    }

    public function getTransactions()
    {
        abort_unless(RealEstateHelper::isEnabledCreditsSystem(), 404);

        $this->pageTitle(trans('plugins/real-estate::account.transactions'));

        Assets::addScriptsDirectly('vendor/core/plugins/real-estate/js/components.js');

        Assets::usingVueJS();

        return view($this->getViewFileName('dashboard.settings.transactions'));
    }

    public function ajaxGetPackages()
    {
        abort_unless(RealEstateHelper::isEnabledCreditsSystem(), 404);

        if (is_plugin_active('language')) {
            Language::setCurrentAdminLocale(Language::getCurrentLocaleCode());
        }

        $account = Account::query()->with(['packages'])->findOrFail(auth('account')->id());

        $packages = Package::query()
            ->wherePublished()
            ->get();

        $packages = $packages->filter(function ($package) use ($account) {
            return empty($package->account_limit)
                || $account->packages->where('id', $package->id)->count() < $package->account_limit;
        });

        return $this
            ->httpResponse()
            ->setData([
                'packages' => PackageResource::collection($packages),
                'account' => new AccountResource($account),
            ]);
    }

    public function ajaxSubscribePackage(Request $request)
    {
        abort_unless(RealEstateHelper::isEnabledCreditsSystem(), 404);

        /**
         * @var Package $package
         */
        $package = Package::query()->findOrFail($request->input('id'));

        /**
         * @var Account $account
         */
        $account = Account::query()->findOrFail(auth('account')->id());

        abort_if($package->account_limit
        && $account->packages()->where('package_id', $package->getKey())->count() >= $package->account_limit, 403);

        session(['subscribed_packaged_id' => $package->id]);

        if ((float) $package->price) {
            return $this
                ->httpResponse()
                ->setData(['next_page' => route('public.account.package.subscribe', $package->id)]);
        }

        $this->savePayment($package, null, true);

        return $this
            ->httpResponse()
            ->setData(new AccountResource($account->refresh()))
            ->setMessage(trans('plugins/real-estate::package.add_credit_success'));
    }

    protected function savePayment(Package $package, ?string $chargeId, bool $force = false)
    {
        abort_unless(RealEstateHelper::isEnabledCreditsSystem(), 404);

        $payment = Payment::query()
            ->where('charge_id', $chargeId)
            ->first();

        if (! $payment && ! $force) {
            return false;
        }

        $account = auth('account')->user();

        if (($payment && $payment->status == PaymentStatusEnum::COMPLETED) || $force) {
            $account->credits += $package->number_of_listings;
            $account->save();

            $account->packages()->attach($package);

            Transaction::query()->create([
                'user_id' => 0,
                'account_id' => auth('account')->id(),
                'credits' => $package->number_of_listings,
                'payment_id' => $payment?->id,
            ]);

            $emailHandler = EmailHandler::setModule(REAL_ESTATE_MODULE_SCREEN_NAME)
                ->setVariableValues([
                    'account_name' => $account->name,
                    'account_email' => $account->email,
                    'package_name' => $package->name,
                    'package_price' => $package->price,
                    'package_percent_discount' => $package->percent_save,
                    'package_number_of_listings' => $package->number_of_listings,
                    'package_price_per_credit' => $package->price ? $package->price / ($package->number_of_listings ?: 1) : 0,
                ]);

            if (! $package->price) {
                $emailHandler->sendUsingTemplate('free-credit-claimed');
            } else {
                $emailHandler->sendUsingTemplate('payment-received');
            }

            $emailHandler->sendUsingTemplate('payment-receipt', $account->email);

            return true;
        }

        return false;
    }

    public function getSubscribePackage(int|string $id, CouponService $service)
    {
        abort_unless(RealEstateHelper::isEnabledCreditsSystem(), 404);

        Assets::addScripts('form-validation');

        $package = Package::query()->findOrFail($id);

        Session::put('cart_total', $package->price);

        $this->pageTitle(trans('plugins/real-estate::package.subscribe_package', ['name' => $package->name]));

        add_filter(PAYMENT_FILTER_AFTER_PAYMENT_METHOD, function () use ($service, $package) {
            $totalAmount = $service->getAmountAfterDiscount(
                Session::get('coupon_discount_amount', 0),
                $package->price
            );

            return view('plugins/real-estate::coupons.partials.form', compact('package', 'totalAmount'));
        });

        return view('plugins/real-estate::account.checkout', compact('package'));
    }

    public function getPackageSubscribeCallback($packageId, Request $request)
    {
        abort_unless(RealEstateHelper::isEnabledCreditsSystem(), 404);

        /**
         * @var Package $package
         */
        $package = Package::query()->findOrFail($packageId);

        if (is_plugin_active('paypal') && $request->input('type') == PAYPAL_PAYMENT_METHOD_NAME) {
            $validator = Validator::make($request->input(), [
                'amount' => ['required', 'numeric'],
                'currency' => ['required'],
            ]);

            if ($validator->fails()) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage($validator->getMessageBag()->first());
            }

            $payPalService = app(PayPalPaymentService::class);

            $paymentStatus = $payPalService->getPaymentStatus($request);

            if ($paymentStatus) {
                $chargeId = session('paypal_payment_id');

                $payPalService->afterMakePayment($request->input());

                $this->savePayment($package, $chargeId);

                return $this
                    ->httpResponse()
                    ->setNextUrl(route('public.account.packages'))
                    ->setMessage(trans('plugins/real-estate::package.add_credit_success'));
            }

            return $this
                ->httpResponse()
                ->setError()
                ->setNextUrl(route('public.account.packages'))
                ->setMessage($payPalService->getErrorMessage());
        }

        $this->savePayment($package, $request->input('charge_id'));

        if (! $request->has('success') || $request->input('success')) {
            return $this
                ->httpResponse()
                ->setNextUrl(route('public.account.packages'))
                ->setMessage(session()->get('success_msg') ?: trans('plugins/real-estate::package.add_credit_success'));
        }

        return $this
            ->httpResponse()
            ->setError()
            ->setNextUrl(route('public.account.packages'))
            ->setMessage(__('Payment failed!'));
    }

    public function postSecurity(UpdatePasswordRequest $request)
    {
        $request->user('account')->update([
            'password' => $request->input('password'),
        ]);

        AccountActivityLog::query()->create(['action' => 'update_security']);

        return $this
            ->httpResponse()
            ->setNextUrl(route('public.account.settings'))
            ->setMessage(trans('plugins/real-estate::dashboard.password_update_success'));
    }

    public function postAvatar(AvatarRequest $request)
    {
        try {
            $account = auth('account')->user();

            $result = RvMedia::uploadFromBlob($request->file('avatar_file'), folderSlug: auth('account')->user()->upload_folder);

            if ($result['error']) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage($result['message']);
            }

            $file = $result['data'];

            $avatar = MediaFile::query()->find($account->avatar_id);

            $avatar?->forceDelete();

            $account->avatar_id = $file->id;

            $account->save();

            AccountActivityLog::query()->create([
                'action' => 'changed_avatar',
            ]);

            return $this
                ->httpResponse()
                ->setMessage(trans('plugins/real-estate::dashboard.update_avatar_success'))
                ->setData(['url' => Storage::url($file->url)]);
        } catch (Exception $ex) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($ex->getMessage());
        }
    }

    public function getActivityLogs()
    {
        $activities = AccountActivityLog::query()
            ->where('account_id', auth('account')->id())
            ->latest()
            ->paginate();

        Assets::addScriptsDirectly('vendor/core/plugins/real-estate/js/components.js');

        Assets::usingVueJS();

        return $this
            ->httpResponse()
            ->setData(ActivityLogResource::collection($activities))->toApiResponse();
    }

    public function postUpload(Request $request)
    {
        if (setting('media_chunk_enabled') != '1') {
            $validator = Validator::make($request->all(), [
                'file.0' => ['required', 'image', 'mimes:jpg,jpeg,png,webp'],
            ]);

            if ($validator->fails()) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage($validator->getMessageBag()->first());
            }

            $result = RvMedia::handleUpload(Arr::first($request->file('file')), 0, auth('account')->user()->upload_folder);

            if ($result['error']) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage($result['message']);
            }

            return $this
                ->httpResponse()
                ->setData($result['data']);
        }

        try {
            // Create the file receiver
            $receiver = new FileReceiver('file', $request, DropZoneUploadHandler::class);
            // Check if the upload is success, throw exception or return response you need
            if ($receiver->isUploaded() === false) {
                throw new UploadMissingFileException();
            }
            // Receive the file
            $save = $receiver->receive();
            // Check if the upload has finished (in chunk mode it will send smaller files)
            if ($save->isFinished()) {
                $result = RvMedia::handleUpload($save->getFile(), 0, auth('account')->user()->upload_folder);

                if (! $result['error']) {
                    return $this
                        ->httpResponse()
                        ->setData($result['data']);
                }

                return $this
                    ->httpResponse()
                    ->setError()->setMessage($result['message']);
            }
            // We are in chunk mode, lets send the current progress
            $handler = $save->handler();

            return response()->json([
                'done' => $handler->getPercentageDone(),
                'status' => true,
            ]);
        } catch (Exception $exception) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function postUploadFromEditor(Request $request)
    {
        return RvMedia::uploadFromEditor($request, 0, auth('account')->user()->upload_folder);
    }

    public function postUploadVideo(Request $request)
    {
        if (setting('media_chunk_enabled') != '1') {
            $validator = Validator::make($request->all(), [
                'file' => ['required', 'file', 'mimes:mp4,m4v,mov,webm,avi,wmv,flv,mkv'],
            ]);

            if ($validator->fails()) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage($validator->getMessageBag()->first());
            }

            $result = RvMedia::handleUpload($request->file('file'), 0, auth('account')->user()->upload_folder);

            if ($result['error']) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage($result['message']);
            }

            // Generate video thumbnail
            $thumbnailResult = $this->generateVideoThumbnail($result['data']['url'], auth('account')->user()->upload_folder);

            // Add thumbnail URL to response data if generation was successful
            if (!$thumbnailResult['error']) {
                $result['data']['thumbnail_url'] = $thumbnailResult['thumbnail_url'];
            }

            return $this
                ->httpResponse()
                ->setData($result['data']);
        }

        try {
            // Create the file receiver
            $receiver = new FileReceiver('file', $request, DropZoneUploadHandler::class);
            // Check if the upload is success, throw exception or return response you need
            if ($receiver->isUploaded() === false) {
                throw new UploadMissingFileException();
            }
            // Receive the file
            $save = $receiver->receive();
            // Check if the upload has finished (in chunk mode it will send smaller files)
            if ($save->isFinished()) {
                $result = RvMedia::handleUpload($save->getFile(), 0, auth('account')->user()->upload_folder);

                if (! $result['error']) {
                    // Generate video thumbnail
                    $thumbnailResult = $this->generateVideoThumbnail($result['data']['url'], auth('account')->user()->upload_folder);

                    // Add thumbnail URL to response data if generation was successful
                    if (!$thumbnailResult['error']) {
                        $result['data']['thumbnail_url'] = $thumbnailResult['thumbnail_url'];
                    }

                    return $this
                        ->httpResponse()
                        ->setData($result['data']);
                }

                return $this
                    ->httpResponse()
                    ->setError()->setMessage($result['message']);
            }
            // We are in chunk mode, lets send the current progress
            $handler = $save->handler();

            return response()->json([
                'done' => $handler->getPercentageDone(),
                'status' => true,
            ]);
        } catch (Exception $exception) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function ajaxGetTransactions()
    {
        abort_unless(RealEstateHelper::isEnabledCreditsSystem(), 404);

        $transactions = Transaction::query()
            ->where('account_id', auth('account')->id())
            ->latest()
            ->with(['payment', 'user'])
            ->paginate();

        return $this
            ->httpResponse()
            ->setData(TransactionResource::collection($transactions))->toApiResponse();
    }

    public function getPendingApproval()
    {
        $this->pageTitle(trans('plugins/real-estate::account.pending_approval.title'));

        return view($this->getViewFileName('dashboard.pending-approval'));
    }

    /**
     * Generate video thumbnail using VideoThumbnailService
     *
     * @param string $videoUrl
     * @param string|null $folderSlug
     * @return array
     */
    protected function generateVideoThumbnail(string $videoUrl, ?string $folderSlug = null): array
    {
        try {
            $videoThumbnailService = app(VideoThumbnailService::class);

            // Get the real path of the video file (could be local path or URL for cloud storage)
            $videoPath = RvMedia::getRealPath($videoUrl);

            if (!$videoPath) {
                return [
                    'error' => true,
                    'message' => 'Video file path could not be determined.',
                ];
            }

            // For cloud storage, getRealPath returns a URL, which is fine for our updated service
            // For local storage, it returns a file path, which we need to check exists
            if (!Str::contains($videoPath, ['http://', 'https://']) && !file_exists($videoPath)) {
                return [
                    'error' => true,
                    'message' => 'Local video file not found for thumbnail generation.',
                ];
            }

            // Generate and upload thumbnail
            return $videoThumbnailService->generateOptimalThumbnail($videoPath, 0, $folderSlug);

        } catch (Exception $exception) {
            // Log the error but don't fail the video upload
            Log::error('Video thumbnail generation failed', [
                'video_url' => $videoUrl,
                'error' => $exception->getMessage(),
            ]);

            return [
                'error' => true,
                'message' => 'Failed to generate video thumbnail: ' . $exception->getMessage(),
            ];
        }
    }

    /**
     * Delete video file from server
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function postDeleteVideo(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'video_url' => ['required', 'string'],
            ]);

            if ($validator->fails()) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage($validator->getMessageBag()->first());
            }

            $videoUrl = $request->input('video_url');

            // Find the media file by URL
            $mediaFile = MediaFile::where('url', $videoUrl)->first();

            if (!$mediaFile) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage('Video file not found.');
            }

            // Check if the current user owns this file (security check)
            $userFolder = auth('account')->user()->upload_folder;
            if ($userFolder && !str_contains($mediaFile->url, $userFolder)) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage('You do not have permission to delete this file.');
            }

            // Delete thumbnail if exists
            if (isset($mediaFile->options['video_thumbnail'])) {
                $thumbnailFile = MediaFile::where('url', $mediaFile->options['video_thumbnail'])->first();
                if ($thumbnailFile) {
                    RvMedia::deleteFile($thumbnailFile);
                    $thumbnailFile->delete();
                }
            }

            // Delete the video file
            RvMedia::deleteFile($mediaFile);
            $mediaFile->delete();

            return $this
                ->httpResponse()
                ->setMessage('Video deleted successfully.');

        } catch (Exception $exception) {
            Log::error('Video deletion failed', [
                'video_url' => $request->input('video_url'),
                'error' => $exception->getMessage(),
            ]);

            return $this
                ->httpResponse()
                ->setError()
                ->setMessage('Failed to delete video: ' . $exception->getMessage());
        }
    }
}
