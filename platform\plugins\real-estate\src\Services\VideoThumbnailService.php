<?php

namespace Xmetr\RealEstate\Services;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Media\Facades\RvMedia;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;
use Throwable;

class VideoThumbnailService
{
    /**
     * Generate a thumbnail from a video file using FFmpeg
     *
     * @param string $videoPath The path to the video file
     * @param string|null $outputPath Optional custom output path for the thumbnail
     * @param int $timeOffset Time offset in seconds to capture the thumbnail (default: 5 seconds)
     * @return array Result array with success status and thumbnail path or error message
     */
    public function generateThumbnail(string $videoPath, ?string $outputPath = null, int $timeOffset = 5): array
    {
        try {
            // Check if FFmpeg is available
            if (!$this->isFFmpegAvailable()) {
                return [
                    'error' => true,
                    'message' => 'FFmpeg is not available on this system. Please install FFmpeg to enable video thumbnail generation.',
                ];
            }

            // Validate video file exists
            if (!File::exists($videoPath)) {
                return [
                    'error' => true,
                    'message' => 'Video file not found: ' . $videoPath,
                ];
            }

            // Generate output path if not provided
            if (!$outputPath) {
                $videoInfo = pathinfo($videoPath);
                $thumbnailName = $videoInfo['filename'] . '_thumbnail.jpg';
                $outputPath = $videoInfo['dirname'] . '/' . $thumbnailName;
            }

            // Ensure output directory exists
            $outputDir = dirname($outputPath);
            if (!File::exists($outputDir)) {
                File::makeDirectory($outputDir, 0755, true);
            }

            // Build FFmpeg command to extract thumbnail
            $command = [
                'ffmpeg',
                '-i', $videoPath,
                '-ss', (string) $timeOffset, // Seek to specified time offset
                '-vframes', '1', // Extract only one frame
                '-q:v', '2', // High quality
                '-vf', 'scale=640:360', // Scale to reasonable thumbnail size
                '-y', // Overwrite output file if it exists
                $outputPath
            ];

            // Execute FFmpeg command
            $process = new Process($command);
            $process->setTimeout(60); // 60 seconds timeout
            $process->run();

            if (!$process->isSuccessful()) {
                Log::error('FFmpeg thumbnail generation failed', [
                    'command' => $process->getCommandLine(),
                    'error' => $process->getErrorOutput(),
                    'video_path' => $videoPath,
                ]);

                return [
                    'error' => true,
                    'message' => 'Failed to generate video thumbnail: ' . $process->getErrorOutput(),
                ];
            }

            // Verify thumbnail was created
            if (!File::exists($outputPath)) {
                return [
                    'error' => true,
                    'message' => 'Thumbnail file was not created successfully.',
                ];
            }

            return [
                'error' => false,
                'thumbnail_path' => $outputPath,
                'message' => 'Video thumbnail generated successfully.',
            ];

        } catch (ProcessFailedException $exception) {
            Log::error('FFmpeg process failed', [
                'exception' => $exception->getMessage(),
                'video_path' => $videoPath,
            ]);

            return [
                'error' => true,
                'message' => 'FFmpeg process failed: ' . $exception->getMessage(),
            ];
        } catch (Throwable $exception) {
            BaseHelper::logError($exception);

            return [
                'error' => true,
                'message' => 'An error occurred while generating video thumbnail: ' . $exception->getMessage(),
            ];
        }
    }

    /**
     * Generate thumbnail and upload it to the media system
     *
     * @param string $videoPath Path to the video file (can be local path or URL)
     * @param int|string|null $folderId Media folder ID to upload the thumbnail to
     * @param string|null $folderSlug Media folder slug
     * @param int $timeOffset Time offset in seconds to capture the thumbnail
     * @return array Result with thumbnail URL or error
     */
    public function generateAndUploadThumbnail(
        string $videoPath,
        int|string|null $folderId = 0,
        ?string $folderSlug = null,
        int $timeOffset = 5
    ): array {
        $tempVideoPath = null;
        $tempThumbnailPath = null;

        try {
            // Check if video path is a URL (remote file)
            $isRemoteFile = Str::contains($videoPath, ['http://', 'https://']);

            if ($isRemoteFile) {
                // Download remote video file to temporary location
                $tempVideoPath = $this->downloadRemoteFile($videoPath);
                if (!$tempVideoPath) {
                    return [
                        'error' => true,
                        'message' => 'Failed to download remote video file for thumbnail generation.',
                    ];
                }
                $actualVideoPath = $tempVideoPath;
            } else {
                // Use local file path directly
                $actualVideoPath = $videoPath;
            }

            // Generate temporary thumbnail
            $tempDir = sys_get_temp_dir();
            $tempThumbnailPath = $tempDir . '/' . Str::uuid() . '_thumbnail.jpg';

            $result = $this->generateThumbnail($actualVideoPath, $tempThumbnailPath, $timeOffset);

            if ($result['error']) {
                return $result;
            }

            // Upload thumbnail to media system
            $uploadResult = RvMedia::uploadFromPath(
                $tempThumbnailPath,
                $folderId,
                $folderSlug,
                'image/jpeg'
            );

            if ($uploadResult['error']) {
                return [
                    'error' => true,
                    'message' => 'Failed to upload thumbnail: ' . $uploadResult['message'],
                ];
            }

            return [
                'error' => false,
                'thumbnail_url' => $uploadResult['data']['url'],
                'thumbnail_data' => $uploadResult['data'],
                'message' => 'Video thumbnail generated and uploaded successfully.',
            ];

        } catch (Throwable $exception) {
            BaseHelper::logError($exception);

            return [
                'error' => true,
                'message' => 'Failed to generate thumbnail: ' . $exception->getMessage(),
            ];
        } finally {
            // Clean up temporary files
            if ($tempVideoPath && File::exists($tempVideoPath)) {
                File::delete($tempVideoPath);
            }
            if ($tempThumbnailPath && File::exists($tempThumbnailPath)) {
                File::delete($tempThumbnailPath);
            }
        }
    }

    /**
     * Check if FFmpeg is available on the system
     *
     * @return bool
     */
    public function isFFmpegAvailable(): bool
    {
        try {
            $process = new Process(['ffmpeg', '-version']);
            $process->run();

            return $process->isSuccessful();
        } catch (Throwable) {
            return false;
        }
    }

    /**
     * Get video duration using FFprobe
     *
     * @param string $videoPath
     * @return float|null Duration in seconds, or null if unable to determine
     */
    public function getVideoDuration(string $videoPath): ?float
    {
        try {
            if (!File::exists($videoPath)) {
                return null;
            }

            $command = [
                'ffprobe',
                '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                $videoPath
            ];

            $process = new Process($command);
            $process->run();

            if ($process->isSuccessful()) {
                $duration = trim($process->getOutput());
                return is_numeric($duration) ? (float) $duration : null;
            }

            return null;
        } catch (Throwable) {
            return null;
        }
    }

    /**
     * Generate thumbnail at optimal time offset (usually 10% into the video)
     *
     * @param string $videoPath
     * @param int|string|null $folderId
     * @param string|null $folderSlug
     * @return array
     */
    public function generateOptimalThumbnail(
        string $videoPath,
        int|string|null $folderId = 0,
        ?string $folderSlug = null
    ): array {
        $duration = $this->getVideoDuration($videoPath);

        // Use 10% into the video, or 5 seconds if duration is unknown/short
        $timeOffset = $duration ? max(5, (int) ($duration * 0.1)) : 5;

        return $this->generateAndUploadThumbnail($videoPath, $folderId, $folderSlug, $timeOffset);
    }

    /**
     * Download a remote file to a temporary location
     *
     * @param string $url The URL of the remote file
     * @return string|null The path to the downloaded temporary file, or null on failure
     */
    protected function downloadRemoteFile(string $url): ?string
    {
        try {
            $response = Http::timeout(120)->get($url);

            if ($response->failed()) {
                Log::error('Failed to download remote video file', [
                    'url' => $url,
                    'status' => $response->status(),
                ]);
                return null;
            }

            // Create temporary file
            $tempDir = sys_get_temp_dir();
            $tempFilePath = $tempDir . '/' . Str::uuid() . '_video_' . basename(parse_url($url, PHP_URL_PATH));

            // Save the downloaded content to temporary file
            if (File::put($tempFilePath, $response->body()) === false) {
                Log::error('Failed to save downloaded video to temporary file', [
                    'url' => $url,
                    'temp_path' => $tempFilePath,
                ]);
                return null;
            }

            return $tempFilePath;

        } catch (Throwable $exception) {
            Log::error('Exception while downloading remote video file', [
                'url' => $url,
                'error' => $exception->getMessage(),
            ]);
            return null;
        }
    }
}
