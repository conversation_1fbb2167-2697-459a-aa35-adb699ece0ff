<?php

namespace Xmetr\RealEstate\Forms;

use Xmetr\Base\Facades\Assets;
use Xmetr\Base\Forms\FieldOptions\AutocompleteFieldOption;
use Xmetr\Base\Forms\FieldOptions\ContentFieldOption;
use Xmetr\Base\Forms\FieldOptions\DescriptionFieldOption;
use Xmetr\Base\Forms\FieldOptions\MediaFileFieldOption;
use Xmetr\Base\Forms\FieldOptions\NameFieldOption;
use Xmetr\Base\Forms\FieldOptions\OnOffFieldOption;
use Xmetr\Base\Forms\FieldOptions\StatusFieldOption;
use Xmetr\Base\Forms\FieldOptions\TextareaFieldOption;
use Xmetr\Base\Forms\Fields\AutocompleteField;
use Xmetr\Base\Forms\Fields\EditorField;
use Xmetr\Base\Forms\Fields\MediaFileField;
use Xmetr\Base\Forms\Fields\OnOffField;
use Xmetr\Base\Forms\Fields\SelectField;
use Xmetr\Base\Forms\Fields\TextareaField;
use Xmetr\Base\Forms\Fields\TextField;
use Xmetr\Base\Forms\FormAbstract;
use Xmetr\Location\Fields\Options\SelectLocationFieldOption;
use Xmetr\Location\Fields\SelectLocationField;
use Xmetr\RealEstate\Enums\CustomFieldEnum;
use Xmetr\RealEstate\Enums\ProjectBuildClassEnum;
use Xmetr\RealEstate\Enums\ProjectStatusEnum;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\Fields\CategoryMultiField;
use Xmetr\RealEstate\Forms\Fronts\Auth\FieldOptions\TextFieldOption;
use Xmetr\RealEstate\Http\Requests\ProjectRequest;
use Xmetr\RealEstate\Models\Currency;
use Xmetr\RealEstate\Models\CustomField;
use Xmetr\RealEstate\Models\Facility;
use Xmetr\RealEstate\Models\Feature;
use Xmetr\RealEstate\Models\Investor;
use Xmetr\RealEstate\Models\Project;
use stdClass;
use Xmetr\Base\Forms\FieldOptions\RadioFieldOption;
use Xmetr\Base\Forms\Fields\RadioField;

class ProjectForm extends FormAbstract
{
    public function setup(): void
    {
        Assets::addStyles(['datetimepicker'])
            ->addScriptsDirectly([
                'vendor/core/plugins/real-estate/js/real-estate.js',
                'vendor/core/plugins/real-estate/js/components.js',
                'vendor/core/plugins/real-estate/js/custom-fields.js',
            ])
            ->addStylesDirectly('vendor/core/plugins/real-estate/css/real-estate.css');

        Assets::usingVueJS();

        $investors = Investor::query()->pluck('name', 'id')->all();

        $currencies = Currency::query()->pluck('title', 'id')->all();

        $selectedFeatures = [];
        if ($this->getModel()) {
            /**
             * @var Project $project
             */
            $project = $this->getModel();

            $selectedFeatures = $project->features()->pluck('id')->all();
        }

        $features = Feature::query()->select(['id', 'name'])->get();

        $facilities = Facility::query()->select(['id', 'name'])->get();
        if ($this->getModel()) {
            /**
             * @var Project $project
             */
            $project = $this->getModel();

            $selectedFacilities = $project->facilities()->select('re_facilities.id', 'distance')->get();
        } else {
            $selectedFacilities = collect();

            $oldSelectedFacilities = old('facilities', []);

            if (! empty($oldSelectedFacilities)) {
                foreach ($oldSelectedFacilities as $oldSelectedFacility) {
                    if (! isset($oldSelectedFacility['id']) || ! isset($oldSelectedFacility['distance'])) {
                        continue;
                    }

                    $item = new stdClass();
                    $item->id = $oldSelectedFacility['id'];
                    $item->distance = $oldSelectedFacility['distance'];

                    $selectedFacilities->add($item);
                }
            }
        }

        $selectedCategories = [];
        if ($this->getModel()) {
            /**
             * @var Project $project
             */
            $project = $this->getModel();

            $selectedCategories = $project->categories()->pluck('category_id')->all();
        }

        if (! $this->formHelper->hasCustomField('categoryMulti')) {
            $this->formHelper->addCustomField('categoryMulti', CategoryMultiField::class);
        }

        $customFields = CustomField::query()->select(['name', 'id', 'type'])->get();

        $this
            ->model(Project::class)
            ->setValidatorClass(ProjectRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required()->toArray())
            ->add('description', TextareaField::class, DescriptionFieldOption::make()->toArray())
            ->add(
                'is_featured',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('core/base::forms.is_featured'))
                    ->defaultValue(false)
                    ->toArray()
            )
            ->add('content', EditorField::class, ContentFieldOption::make()->allowedShortcodes()->toArray())
            ->add('images[]', 'mediaImages', [
                'label' => trans('plugins/real-estate::property.form.images'),
                'values' => $this->getModel()->id ? $this->getModel()->images : [],
            ])
            ->add(
                'video',
                MediaFileField::class,
                MediaFileFieldOption::make()
                    ->label(trans('plugins/real-estate::property.form.video'))
                    ->value($this->getModel()->id ? $this->getModel()->video : null)
            )
            ->when(is_plugin_active('location'), function (FormAbstract $form): void {
                $form->add(
                    'location_data',
                    SelectLocationField::class,
                    SelectLocationFieldOption::make()->toArray()
                );
            })
            ->add(
                'location',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/real-estate::project.form.location'))
                    ->placeholder(trans('plugins/real-estate::project.form.location'))
                    ->maxLength(191)
            )
            ->add('rowOpen', 'html', [
                'html' => '<div class="row">',
            ])
            ->add('latitude', 'text', [
                'label' => trans('plugins/real-estate::property.form.latitude'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-6',
                ],
                'attr' => [
                    'placeholder' => 'Ex: 1.462260',
                    'data-counter' => 25,
                ],
                'help_block' => [
                    'text' => trans('plugins/real-estate::property.form.latitude_helper'),
                    'attr' => [
                        'href' => 'https://www.latlong.net/convert-address-to-lat-long.html',
                        'target' => '_blank',
                        'rel' => 'nofollow',
                    ],
                ],
            ])
            ->add('longitude', 'text', [
                'label' => trans('plugins/real-estate::property.form.longitude'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-6',
                ],
                'attr' => [
                    'placeholder' => 'Ex: 103.812530',
                    'data-counter' => 25,
                ],
                'help_block' => [
                    'text' => trans('plugins/real-estate::property.form.longitude_helper'),
                    'attr' => [
                        'href' => 'https://www.latlong.net/convert-address-to-lat-long.html',
                        'target' => '_blank',
                        'rel' => 'nofollow',
                    ],
                ],
            ])
            ->add('rowClose', 'html', [
                'html' => '</div>',
            ])
            ->add('rowOpen1', 'html', [
                'html' => '<div class="row">',
            ])
            ->add('number_block', 'number', [
                'label' => trans('plugins/real-estate::project.form.number_block'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-4',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::project.form.number_block'),
                ],
            ])
            ->add('number_floor', 'number', [
                'label' => trans('plugins/real-estate::project.form.number_floor'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-4',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::project.form.number_floor'),
                ],
            ])
            ->add('number_flat', 'number', [
                'label' => trans('plugins/real-estate::project.form.number_flat'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-4',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::project.form.number_flat'),
                ],
            ])
            ->add('rowClose1', 'html', [
                'html' => '</div>',
            ])
            ->add('rowOpen3', 'html', [
                'html' => '<div class="row">',
            ])
            ->add('parking', 'number', [
                'label' => trans('plugins/real-estate::project.form.parking'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-4',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::project.form.parking'),
                    'min' => 0,
                ],
            ])
            ->add('year_built', 'number', [
                'label' => trans('plugins/real-estate::project.form.year_built'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-4',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::project.form.year_built')
                ],
            ])
            ->add('build_class', 'customSelect', [
                'label' => trans('plugins/real-estate::project.form.build_class'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-4',
                ],
                'choices' => ProjectBuildClassEnum::labels(),
                'empty_value' => trans('plugins/real-estate::project.form.select_build_class'),
            ])
            ->add('rowClose3', 'html', [
                'html' => '</div>',
            ])
            ->add('rowOpen2', 'html', [
                'html' => '<div class="row">',
            ])
            // ->add('price_from', 'text', [
            //     'label' => trans('plugins/real-estate::project.form.price_from'),
            //     'wrapper' => [
            //         'class' => 'form-group mb-3 col-md-4',
            //     ],
            //     'attr' => [
            //         'placeholder' => trans('plugins/real-estate::project.form.price_from'),
            //         'class' => 'form-control input-mask-number',
            //         'data-thousands-separator' => RealEstateHelper::getThousandSeparatorForInputMask(),
            //         'data-decimal-separator' => RealEstateHelper::getDecimalSeparatorForInputMask(),
            //     ],
            // ])
            // ->add('price_to', 'text', [
            //     'label' => trans('plugins/real-estate::project.form.price_to'),
            //     'wrapper' => [
            //         'class' => 'form-group mb-3 col-md-4',
            //     ],
            //     'attr' => [
            //         'placeholder' => trans('plugins/real-estate::project.form.price_to'),
            //         'class' => 'form-control input-mask-number',
            //         'data-thousands-separator' => RealEstateHelper::getThousandSeparatorForInputMask(),
            //         'data-decimal-separator' => RealEstateHelper::getDecimalSeparatorForInputMask(),
            //     ],
            // ])
            // ->add('currency_id', 'customSelect', [
            //     'label' => trans('plugins/real-estate::project.form.currency'),
            //     'wrapper' => [
            //         'class' => 'form-group mb-3 col-md-4',
            //     ],
            //     'attr' => [
            //         'class' => 'form-control select-full',
            //     ],
            //     'choices' => $currencies,
            // ])
            // ->add(
            //     'private_notes',
            //     TextareaField::class,
            //     TextareaFieldOption::make()
            //         ->label(trans('plugins/real-estate::property.private_notes'))
            //         ->helperText(trans('plugins/real-estate::property.private_notes_helper'))
            //         ->rows(2)
            //         ->colspan(2)
            //         ->toArray()
            // )
            ->add('rowClose2', 'html', [
                'html' => '</div>',
            ])
            ->addMetaBoxes([
                'features' => [
                    'title' => trans('plugins/real-estate::property.form.features'),
                    'content' => view(
                        'plugins/real-estate::partials.form-features',
                        compact('selectedFeatures', 'features')
                    )->render(),
                    'priority' => 1,
                ],
                'facilities' => [
                    'title' => trans('plugins/real-estate::project.distance_key'),
                    'content' => view(
                        'plugins/real-estate::partials.form-facilities',
                        compact('facilities', 'selectedFacilities')
                    ),
                    'priority' => 0,
                ],
            ])
            // ->add('status', SelectField::class, StatusFieldOption::make()->choices(ProjectStatusEnum::labels())->toArray())
            ->add(
                'status',
                RadioField::class,
                RadioFieldOption::make()
                    ->choices(ProjectStatusEnum::labels())
                    ->selected((string) $this->model->status ?: ProjectStatusEnum::NOT_AVAILABLE)
                    ->attributes(['class' => 'custom-radio'])
                    ->wrapperAttributes(['class' => 'mb-3'])
                    ->metadata()
            )
            // ->add('categories[]', 'categoryMulti', [
            //     'label' => trans('plugins/real-estate::project.form.categories'),
            //     'choices' => get_property_categories_with_children(),
            //     'value' => old('categories', $selectedCategories),
            // ])
            // ->add('investor_id', 'customSelect', [
            //     'label' => trans('plugins/real-estate::project.form.investor'),
            //     'attr' => [
            //         'class' => 'form-control select-search-full',
            //     ],
            //     'choices' => [0 => trans('plugins/real-estate::project.select_investor')] + $investors,
            // ])
            ->add(
                'unique_id',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/real-estate::project.unique_id'))
                    ->placeholder(trans('plugins/real-estate::project.unique_id'))
                    ->value($this->getModel()->getKey() ? $this->getModel()->unique_id : $this->getModel()->generateUniqueId())
                    ->maxLength(120)
            )
            // ->add('date_finish', 'datePicker', [
            //     'label' => trans('plugins/real-estate::project.form.date_finish'),
            // ])
            // ->add('date_sell', 'datePicker', [
            //     'label' => trans('plugins/real-estate::project.form.date_sell'),
            // ])
            ->setBreakFieldPoint('status')
            ->add(
                'author_id',
                AutocompleteField::class,
                AutocompleteFieldOption::make()
                    ->label(trans('plugins/real-estate::property.account'))
                    ->ajaxUrl(route('account.get-developer-list'))
                    ->when($this->getModel()->author?->id, function (AutocompleteFieldOption $option): void {
                        $option->choices([$this->getModel()->author->id => $this->getModel()->author->name]);
                    })
                    ->emptyValue(trans('plugins/real-estate::property.select_account'))
                    ->allowClear()
            );

        if (RealEstateHelper::isEnabledCustomFields()) {
            $this->addMetaBoxes([
                'custom_fields_box' => [
                    'title' => trans('plugins/real-estate::custom-fields.name'),
                    'content' => view('plugins/real-estate::custom-fields.custom-fields', [
                        'options' => CustomFieldEnum::labels(),
                        'customFields' => $customFields,
                        'model' => $this->getModel(),
                        'ajax' => is_in_admin(true) ? route('real-estate.custom-fields.get-info') : route('public.account.custom-fields.get-info'),
                    ]),
                    'priority' => 0,
                ],
            ]);
        }
    }
}
