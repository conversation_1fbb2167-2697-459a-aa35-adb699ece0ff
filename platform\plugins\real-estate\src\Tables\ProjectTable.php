<?php

namespace Xmetr\RealEstate\Tables;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Base\Facades\Html;
use Xmetr\RealEstate\Enums\ProjectBuildClassEnum;
use Xmetr\RealEstate\Enums\ProjectStatusEnum;
use Xmetr\RealEstate\Models\Investor;
use Xmetr\RealEstate\Models\Project;
use Xmetr\Table\Abstracts\TableAbstract;
use Xmetr\Table\Actions\DeleteAction;
use Xmetr\Table\Actions\EditAction;
use Xmetr\Table\BulkActions\DeleteBulkAction;
use Xmetr\Table\BulkChanges\NameBulkChange;
use Xmetr\Table\BulkChanges\SelectBulkChange;
use Xmetr\Table\BulkChanges\StatusBulkChange;
use Xmetr\Table\Columns\Column;
use Xmetr\Table\Columns\CreatedAtColumn;
use Xmetr\Table\Columns\FormattedColumn;
use Xmetr\Table\Columns\IdColumn;
use Xmetr\Table\Columns\ImageColumn;
use Xmetr\Table\Columns\NameColumn;
use Xmetr\Table\Columns\StatusColumn;
use Xmetr\Table\Columns\EnumColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class ProjectTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Project::class)
            ->addActions([
                EditAction::make()->route('project.edit'),
                DeleteAction::make()->route('project.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('views', function (Project $item) {
                return number_format($item->views);
            })
            ->editColumn('unique_id', function (Project $item) {
                return BaseHelper::clean($item->unique_id ?: '&mdash;');
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'images',
                'views',
                'whatsapp_clicks',
                'telegram_clicks',
                'phone_clicks',
                'status',
                'created_at',
                'unique_id',
                'parking',
                'year_built',
                'build_class',
                'district_id',
            ])
            ->with(['district']);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            ImageColumn::make()
                ->searchable(false)
                ->orderable(false),
            NameColumn::make()->route('project.edit'),
            FormattedColumn::make('district_id')
                ->title(trans('plugins/location::district.district'))
                ->alignStart()
                ->getValueUsing(function (FormattedColumn $column) {
                    $item = $column->getItem();
                    if (! $item->district || ! $item->district->name) {
                        return '&mdash;';
                    }

                    return Html::link(route('district.edit', $item->district->id), $item->district->name);
                }),
            Column::make('views')
                ->title(trans('plugins/real-estate::project.views')),
            Column::make('whatsapp_clicks')
                ->title(__('WhatsApp'))
                ->width(120),
            Column::make('telegram_clicks')
                ->title(__('Telegram'))
                ->width(120),
            Column::make('phone_clicks')
                ->title(__('Calls'))
                ->width(120),
            // Column::make('unique_id')
            //     ->title(trans('plugins/real-estate::project.unique_id')),
            Column::make('parking')
                ->title(trans('plugins/real-estate::project.form.parking')),
            Column::make('year_built')
                ->title(trans('plugins/real-estate::project.form.year_built')),
            EnumColumn::make('build_class')
                ->title(trans('plugins/real-estate::project.form.build_class'))
                ->width(120),
            CreatedAtColumn::make(),
            StatusColumn::make(),
        ];
    }

    public function buttons(): array
    {
        $buttons = $this->addCreateButton(route('project.create'), 'project.create');

        if ($this->hasPermission('import-projects.index')) {
            $buttons['import'] = [
                'link' => route('projects.import.index'),
                'text' =>
                    BaseHelper::renderIcon('ti ti-upload')
                    . trans('plugins/real-estate::project.import_projects'),
            ];
        }

        if ($this->hasPermission('export-projects.index')) {
            $buttons['export'] = [
                'link' => route('export-projects.index'),
                'text' =>
                    BaseHelper::renderIcon('ti ti-download')
                    . trans('plugins/real-estate::project.export_projects'),
            ];
        }

        return $buttons;
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('project.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            NameBulkChange::make(),
            StatusBulkChange::make()
                ->choices(ProjectStatusEnum::labels()),
            SelectBulkChange::make()
                ->name('investor_id')
                ->title(trans('plugins/real-estate::project.form.investor'))
                ->searchable()
                ->choices(fn () => Investor::query()->pluck('name', 'id')->all()),
        ];
    }
}
