<?php

namespace Xmetr\Location\Http\Resources;

use Xmetr\Location\Models\City;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin City
 */
class CityResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->getKey(),
            'name' => $this->name,
            'slug' => $this->slug,
            'url' => $this->url,
            'projects_url' => route('public.projects-by-city', $this->slug),
        ];
    }
}
