<?php

namespace Xmetr\RealEstate\Models;

use Xmetr\Base\Casts\SafeContent;
use Xmetr\Base\Models\BaseModel;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\RealEstate\Enums\ProjectBuildClassEnum;
use Xmetr\RealEstate\Enums\ProjectStatusEnum;
use Xmetr\RealEstate\Models\Traits\UniqueId;
use Xmetr\RealEstate\QueryBuilders\ProjectBuilder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\Country;
use Xmetr\Location\Models\District;
use Xmetr\Location\Models\State;
use Xmetr\RealEstate\Enums\ReviewStatusEnum;

/**
 * @method static \Xmetr\RealEstate\QueryBuilders\ProjectBuilder<static> query()
 */
class Project extends BaseModel
{
    use UniqueId;

    protected $table = 're_projects';

    protected $fillable = [
        'name',
        'description',
        'content',
        'location',
        'images',
        'video',
        'video_thumbnail',
        'status',
        'is_featured',
        'investor_id',
        'number_block',
        'number_floor',
        'number_flat',
        'date_finish',
        'date_sell',
        'price_from',
        'price_to',
        'currency_id',
        'city_id',
        'district_id',
        'state_id',
        'country_id',
        'author_id',
        'author_type',
        'category_id',
        'latitude',
        'longitude',
        'unique_id',
        'private_notes',
        'parking',
        'year_built',
        'build_class',
        'whatsapp_clicks',
        'telegram_clicks',
        'phone_clicks',
    ];

    protected $casts = [
        'status' => ProjectStatusEnum::class,
        'date_finish' => 'datetime',
        'date_sell' => 'datetime',
        'price_from' => 'float',
        'price_to' => 'float',
        'number_block' => 'int',
        'number_float' => 'int',
        'number_flat' => 'int',
        'views' => 'int',
        'is_featured' => 'boolean',
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'content' => SafeContent::class,
        'location' => SafeContent::class,
        'private_notes' => SafeContent::class,
        'images' => 'json',
        'parking' => 'int',
        'year_built' => 'int',
        'build_class' => ProjectBuildClassEnum::class,
        'whatsapp_clicks' => 'int',
        'telegram_clicks' => 'int',
        'phone_clicks' => 'int',
    ];

    protected static function booted(): void
    {
        static::deleting(function (Project $project): void {
            $project->categories()->detach();
            $project->customFields()->delete();
            $project->reviews()->delete();
            $project->features()->detach();
            $project->facilities()->detach();
            $project->properties()->update(['project_id' => 0]);
            $project->metadata()->delete();
        });
    }

    public function author(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @deprecated
     */
    public function property(): HasMany
    {
        return $this->properties();
    }

    public function properties(): HasMany
    {
        return $this->hasMany(Property::class, 'project_id');
    }

    public function investor(): BelongsTo
    {
        return $this->belongsTo(Investor::class)->withDefault();
    }

    public function features(): BelongsToMany
    {
        return $this->belongsToMany(Feature::class, 're_project_features', 'project_id', 'feature_id');
    }



    public function facilities(): BelongsToMany
    {
        return $this->morphToMany(Facility::class, 'reference', 're_facilities_distances')->withPivot('distance');
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class)->withDefault();
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 're_project_categories');
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class)->withDefault();
    }

    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class)->withDefault();
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class)->withDefault();
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class)->withDefault();
    }

    protected function image(): Attribute
    {
        return Attribute::make(
            get: function () {
                return Arr::first($this->images) ?? null;
            },
        );
    }

    protected function address(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->location;
            },
        );
    }

    protected function category(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->categories->first() ?: new Category();
            },
        );
    }

    protected function statusHtml(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->status->toHtml();
            },
        );
    }

    protected function categoryName(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->category->name;
            },
        );
    }

    protected function imageThumb(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->image ? RvMedia::getImageUrl($this->image, 'thumb', false, RvMedia::getDefaultImage()) : null;
            },
        );
    }

    protected function imageSmall(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->image ? RvMedia::getImageUrl($this->image, 'small', false, RvMedia::getDefaultImage()) : null;
            },
        );
    }

    protected function mapIcon(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->name;
            },
        );
    }

    protected function cityName(): Attribute
    {
        return Attribute::make(
            get: function () {
                return ($this->city->name ? $this->city->name . ', ' : null) . $this->state->name;
            },
        );
    }

    public function customFields(): MorphMany
    {
        return $this->morphMany(CustomFieldValue::class, 'reference', 'reference_type', 'reference_id')->with('customField.options');
    }

    protected function customFieldsArray(): Attribute
    {
        return Attribute::make(
            get: function () {
                return CustomFieldValue::getCustomFieldValuesArray($this);
            },
        );
    }

    public function reviews(): MorphMany
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    public function newEloquentBuilder($query): ProjectBuilder
    {
        return new ProjectBuilder($query);
    }

    protected function formattedPrice(): Attribute
    {
        return Attribute::get(function () {
            $text = '';

            if ($this->price_from) {
                $text .= format_price($this->price_from, $this->currency);
            }

            if ($this->price_to) {
                $text .= sprintf(' - %s', format_price($this->price_to, $this->currency));
            }

            return $text;
        });
    }

    protected function shortAddress(): Attribute
    {
        return Attribute::get(fn () => implode(', ', array_filter([$this->city->name, $this->state->name])));
    }


    /**
     * Get today's views count for this property
     */
    public function getTodayViewsCount(): int
    {
        return PropertyDailyView::getViewsForDate($this->getKey());
    }

     /**
     * Get formatted views text with total and today's count
     */
    protected function viewsText(): Attribute
    {
        return Attribute::get(function () {
            $totalViews = $this->views ?: 0;
            $todayViews = $this->getTodayViewsCount();

            if ($totalViews === 0) {
                return __('No views');
            }

            if ($totalViews === 1) {
                $text = __(':number view', ['number' => 1]);
            } else {
                $text = __(':number views', ['number' => number_format($totalViews)]);
            }

            if ($todayViews > 0) {
                if ($todayViews === 1) {
                    $text .= '('. __(':number today', ['number' => 1]) . ')';
                } else {
                    $text .= '('. __(':number today', ['number' => number_format($todayViews)]) . ')';
                }
            }

            return $text;
        });
    }

    // public function getBuildClassOptionsAttribute(): array
    // {
    //     return $this->build_class ?? [];
    // }

    // public function getBuildClassLabelsAttribute(): array
    // {
    //     $options = $this->getBuildClassOptionsAttribute();
    //     $labels = [];

    //     foreach ($options as $option) {
    //         if (ProjectBuildClassEnum::isValid($option)) {
    //             $labels[] = (new ProjectBuildClassEnum())->make($option)->label();
    //         }
    //     }

    //     return $labels;
    // }

    public function contactClicks(): MorphMany
    {
        return $this->morphMany(ContactClick::class, 'clickable');
    }

    /**
     * Get today's click count for a specific contact type
     */
    public function getTodayClicksCount(string $contactType): int
    {
        return ContactClick::getClicksForDate($this, $contactType);
    }

    /**
     * Get formatted click text for a contact type with total and today's count
     */
    public function getClicksText(string $contactType): string
    {
        $totalClicks = $this->{$contactType . '_clicks'} ?: 0;
        $todayClicks = $this->getTodayClicksCount($contactType);

        if ($totalClicks === 0) {
            return __('No clicks');
        }

        if ($totalClicks === 1) {
            $text = __(':number click', ['number' => 1]);
        } else {
            $text = __(':number clicks', ['number' => number_format($totalClicks)]);
        }

        if ($todayClicks > 0) {
            if ($todayClicks === 1) {
                $text .= '('. __(':number today', ['number' => 1]) . ')';
            } else {
                $text .= '('. __(':number today', ['number' => number_format($todayClicks)]) . ')';
            }
        }

        return $text;
    }

    protected function reviewsAvgStar(): Attribute
    {
        return Attribute::get(function () {
            return $this->reviews()
                ->where('status', ReviewStatusEnum::APPROVED)
                ->avg('star') ?: 0;
        });
    }

    protected function reviewsCount(): Attribute
    {
        return Attribute::get(function () {
            return $this->reviews()
                ->where('status', ReviewStatusEnum::APPROVED)
                ->count();
        });
    }
}

