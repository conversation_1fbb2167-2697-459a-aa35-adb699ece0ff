<?php

namespace Xmetr\Location\Http\Resources;

use Xmetr\Location\Models\Country;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Country
 */
class CountryResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->getKey(),
            'name' => $this->name,
            'slug' => $this->slug,
            'url' => $this->url,
            'projects_url' => route('public.projects-by-country', $this->slug),
        ];
    }
}
