<?php

namespace Xmetr\RealEstate\Http\Controllers\Fronts;

use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Base\Http\Responses\BaseHttpResponse;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Models\Project;
use Xmetr\RealEstate\Models\ContactClick;
use Throwable;

class ContactClickController extends BaseController
{
    public function trackClick(string $propertyId, string $contactType, BaseHttpResponse $response)
    {
        return $this->trackEntityClick('property', $propertyId, $contactType, $response);
    }

    public function trackProjectClick(string $projectId, string $contactType, BaseHttpResponse $response)
    {
        return $this->trackEntityClick('project', $projectId, $contactType, $response);
    }

    protected function trackEntityClick(string $entityType, string $entityId, string $contactType, BaseHttpResponse $response)
    {
        try {
            // Validate contact type
            if (!in_array($contactType, ['whatsapp', 'telegram', 'phone'])) {
                return $response->setError()->setMessage('Invalid contact type');
            }

            // Find the entity (property or project)
            $entity = $this->findEntity($entityType, $entityId);
            if (!$entity) {
                return $response->setError()->setMessage(ucfirst($entityType) . ' not found');
            }

            // Get the agent/author
            $agent = $entity->author;
            if (!$agent) {
                return $response->setError()->setMessage('Agent not found');
            }

            // Get the contact information
            $contactInfo = null;
            switch ($contactType) {
                case 'whatsapp':
                    $contactInfo = $agent->getMetaData('whatsapp', true);
                    break;
                case 'telegram':
                    $contactInfo = $agent->getMetaData('telegram', true);
                    break;
                case 'phone':
                    $contactInfo = $agent->phone;
                    break;
            }

            if (!$contactInfo) {
                return $response->setError()->setMessage('Contact information not available');
            }

            // Track the click (only if not already tracked in this session)
            $sessionKey = "contact_click_{$entityType}_{$entityId}_{$contactType}";
            if (!session()->has($sessionKey)) {
                // Increment daily click tracking using the new polymorphic model
                ContactClick::incrementClicks($entity, $contactType);

                // Increment total clicks on the entity
                $entity::withoutEvents(fn () => $entity::withoutTimestamps(fn () => $entity->increment($contactType . '_clicks')));

                // Mark as tracked in session to prevent duplicate tracking
                session()->put($sessionKey, time());
            }

            // Generate the appropriate URL
            $redirectUrl = null;
            switch ($contactType) {
                case 'whatsapp':
                    $redirectUrl = "https://wa.me/{$contactInfo}";
                    break;
                case 'telegram':
                    $redirectUrl = "https://t.me/{$contactInfo}";
                    break;
                case 'phone':
                    $redirectUrl = "tel:{$contactInfo}";
                    break;
            }

            return $response->setNextUrl($redirectUrl);

        } catch (Throwable) {
            return $response->setError()->setMessage('An error occurred while processing the request');
        }
    }

    protected function findEntity(string $entityType, string $entityId)
    {
        switch ($entityType) {
            case 'property':
                return Property::query()->findOrFail($entityId);
            case 'project':
                return Project::query()->findOrFail($entityId);
            default:
                return null;
        }
    }
}
