<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migration.
     */
    public function up(): void
    {
        Schema::table('re_projects', function (Blueprint $table): void {
            $table->integer('whatsapp_clicks')->unsigned()->default(0)->after('views');
            $table->integer('telegram_clicks')->unsigned()->default(0)->after('whatsapp_clicks');
            $table->integer('phone_clicks')->unsigned()->default(0)->after('telegram_clicks');
        });
    }

    /**
     * Reverse the migration.
     */
    public function down(): void
    {
        Schema::table('re_projects', function (Blueprint $table): void {
            $table->dropColumn(['whatsapp_clicks', 'telegram_clicks', 'phone_clicks']);
        });
    }
};
