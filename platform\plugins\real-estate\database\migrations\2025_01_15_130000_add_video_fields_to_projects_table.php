<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (! Schema::hasColumn('re_projects', 'video')) {
            Schema::table('re_projects', function (Blueprint $table) {
                $table->string('video')->nullable()->after('images');
                $table->string('video_thumbnail')->nullable()->after('video');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('re_projects', 'video')) {
            Schema::table('re_projects', function (Blueprint $table) {
                $table->dropColumn(['video', 'video_thumbnail']);
            });
        }
    }
};
