<?php

namespace Xmetr\RealEstate\Tables;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Base\Facades\Html;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\RealEstate\Models\Account;
use Xmetr\Table\Abstracts\TableAbstract;
use Xmetr\Table\Actions\DeleteAction;
use Xmetr\Table\Actions\EditAction;
use Xmetr\Table\BulkActions\DeleteBulkAction;
use Xmetr\Table\BulkChanges\CreatedAtBulkChange;
use Xmetr\Table\BulkChanges\EmailBulkChange;
use Xmetr\Table\BulkChanges\TextBulkChange;
use Xmetr\Table\Columns\Column;
use Xmetr\Table\Columns\CreatedAtColumn;
use Xmetr\Table\Columns\EmailColumn;
use Xmetr\Table\Columns\FormattedColumn;
use Xmetr\Table\Columns\IdColumn;
use Xmetr\Table\Columns\NameColumn;
use Xmetr\Table\Columns\YesNoColumn;
use Xmetr\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;
use Xmetr\RealEstate\Enums\AccountTypeEnum;
use Xmetr\Table\BulkChanges\SelectBulkChange;

class AccountTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Account::class)
            ->addActions([
                EditAction::make()->route('account.edit'),
                DeleteAction::make()->route('account.destroy'),
            ])
            ->addHeaderAction(
                CreateHeaderAction::make()
                    ->route('account.create')
                    ->permission('account.create')
            )
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'first_name',
                        'last_name',
                        'email',
                        'phone',
                        'created_at',
                        'credits',
                        'avatar_id',
                        'confirmed_at',
                        'account_type',
                        'last_login',
                    ])
                    ->with(['avatar', 'spokenLanguages'])
                    ->withCount(['properties'])
                    ->when((bool) setting('real_estate_enable_account_verification', false), function (Builder $query): void {
                        $query->whereNotNull('approved_at');
                    })
                    ->when(request()->query('account_type'), function (Builder $query) {
                        if (request()->query('account_type') == '') {
                            $query->whereNotNull('account_type');
                        } else {
                            $query->where('account_type', request()->query('account_type'));
                        }
                    });
            })
            ->addBulkActions([
                DeleteBulkAction::make()->permission('account.destroy'),

            ])
            ->addBulkChanges([
                TextBulkChange::make()
                    ->name('first_name')
                    ->title(trans('plugins/real-estate::account.first_name'))
                    ->validate('required|max:120'),
                TextBulkChange::make()
                    ->name('last_name')
                    ->title(trans('plugins/real-estate::account.last_name'))
                    ->validate('required|max:120'),
                EmailBulkChange::make(),
                SelectBulkChange::make()
                    ->name('account_type')
                    ->title(trans('plugins/real-estate::account.account_type'))
                    ->choices(AccountTypeEnum::labels()),
                CreatedAtBulkChange::make(),
            ])
            ->addColumns([
                IdColumn::make(),
                FormattedColumn::make('avatar_id')
                    ->title(trans('core/base::tables.image'))
                    ->width(70)
                    ->getValueUsing(function (FormattedColumn $column) {
                        return Html::image(
                            RvMedia::getImageUrl($column->getItem()->avatar->url, 'thumb', false, RvMedia::getDefaultImage()),
                            BaseHelper::clean($column->getItem()->name),
                            ['width' => 50]
                        );
                    }),
                NameColumn::make()
                    ->route('account.edit')
                    ->orderable(false)
                    ->searchable(false),
                EmailColumn::make(),
                FormattedColumn::make('account_type')
                    ->title(trans('plugins/real-estate::account.account_type'))
                    ->alignLeft()
                    ->getValueUsing(function (FormattedColumn $column) {
                        return BaseHelper::clean($column->getItem()->account_type->label() ?? '&mdash;');
                    }),
                FormattedColumn::make('phone')
                    ->title(trans('plugins/real-estate::account.phone'))
                    ->alignLeft()
                    ->getValueUsing(function (FormattedColumn $column) {
                        return BaseHelper::clean($column->getItem()->phone ?: '&mdash;');
                    }),
                Column::make('credits')
                    ->title(trans('plugins/real-estate::account.credits'))
                    ->alignLeft(),
                FormattedColumn::make('updated_at')
                    ->title(trans('plugins/real-estate::account.number_of_properties'))
                    ->width(100)
                    ->orderable(false)
                    ->searchable(false)
                    ->getValueUsing(function (FormattedColumn $column) {
                        return $column->getItem()->properties_count;
                    }),
                FormattedColumn::make('spoken_languages')
                    ->title(trans('plugins/real-estate::account.spoken_languages'))
                    ->width(150)
                    ->orderable(false)
                    ->searchable(false)
                    ->getValueUsing(function (FormattedColumn $column) {
                        $languages = $column->getItem()->spokenLanguages;
                        if ($languages->isEmpty()) {
                            return '&mdash;';
                        }
                        return $languages->pluck('name')->join(', ');
                    }),
                CreatedAtColumn::make(),
                FormattedColumn::make('last_login')
                    ->title(trans('plugins/real-estate::account.last_login'))
                    ->alignLeft()
                    ->width(100)
                    ->getValueUsing(function (FormattedColumn $column) {
                        $lastLogin = $column->getItem()->last_login;
                        return $lastLogin ? $lastLogin->format('Y-m-d') : '&mdash;';
                    }),
            ])
            ->onAjax(function (AccountTable $table) {
                return $table->toJson(
                    $table
                        ->table
                        ->eloquent($table->query())
                    ->filter(function (Builder $query) {
                        $keyword = $this->request->input('search.value');

                        if (! $keyword) {
                            return $query;
                        }

                        return $query->where(function (Builder $query) use ($keyword): void {
                            $query
                                ->where('id', $keyword)
                                ->orWhere('first_name', 'LIKE', '%' . $keyword . '%')
                                ->orWhere('last_name', 'LIKE', '%' . $keyword . '%')
                                ->orWhere('email', 'LIKE', '%' . $keyword . '%')
                                ->orWhereDate('created_at', $keyword);
                        });
                    })
                );
            });

        if (setting('verify_account_email', false)) {
            $this->addColumn(
                YesNoColumn::make('confirmed_at')
                    ->title(trans('plugins/real-estate::account.email_verified')),
            );
        }
    }
}
