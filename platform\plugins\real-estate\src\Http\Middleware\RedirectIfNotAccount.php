<?php

namespace Xmetr\RealEstate\Http\Middleware;

use Xmetr\RealEstate\Facades\RealEstateHelper;
use Closure;
use Illuminate\Contracts\Auth\Middleware\AuthenticatesRequests;
use Illuminate\Support\Facades\Auth;

class RedirectIfNotAccount implements AuthenticatesRequests
{
    public function handle($request, Closure $next, $guard = 'account')
    {
        abort_unless(RealEstateHelper::isLoginEnabled(), 404);

        if (! Auth::guard($guard)->check()) {
            if ($request->ajax() || $request->wantsJson()) {
                return response('Unauthorized.', 401);
            }

            // Store the intended URL in session for redirect after login
            session(['url.intended' => $request->fullUrl()]);

            return redirect()->guest(route('public.account.login'));
        }

        return $next($request);
    }
}
