<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migration.
     */
    public function up(): void
    {
        // Create the new polymorphic contact clicks table
        Schema::create('re_contact_clicks', function (Blueprint $table): void {
            $table->id();
            $table->morphs('clickable'); // Creates clickable_id and clickable_type columns
            $table->enum('contact_type', ['whatsapp', 'telegram', 'phone']);
            $table->date('click_date');
            $table->integer('clicks_count')->unsigned()->default(1);
            $table->timestamps();

            // Ensure one record per clickable entity per contact type per day
            $table->unique(['clickable_id', 'clickable_type', 'contact_type', 'click_date'], 'contact_clicks_unique');

            // Index for faster queries
            $table->index(['clickable_id', 'clickable_type', 'contact_type', 'click_date'], 'contact_clicks_index');
            $table->index('click_date', 'contact_clicks_date_index');
        });

        // Migrate existing property contact click data to the new polymorphic table
        if (Schema::hasTable('re_property_contact_clicks')) {
            DB::statement("
                INSERT INTO re_contact_clicks (clickable_id, clickable_type, contact_type, click_date, clicks_count, created_at, updated_at)
                SELECT 
                    property_id as clickable_id,
                    'Xmetr\\\\RealEstate\\\\Models\\\\Property' as clickable_type,
                    contact_type,
                    click_date,
                    clicks_count,
                    created_at,
                    updated_at
                FROM re_property_contact_clicks
            ");
        }
    }

    /**
     * Reverse the migration.
     */
    public function down(): void
    {
        // Restore property contact clicks data if the old table still exists
        if (Schema::hasTable('re_property_contact_clicks')) {
            // Clear existing data first
            DB::table('re_property_contact_clicks')->truncate();
            
            // Migrate back property-specific data
            DB::statement("
                INSERT INTO re_property_contact_clicks (property_id, contact_type, click_date, clicks_count, created_at, updated_at)
                SELECT 
                    clickable_id as property_id,
                    contact_type,
                    click_date,
                    clicks_count,
                    created_at,
                    updated_at
                FROM re_contact_clicks
                WHERE clickable_type = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'
            ");
        }

        Schema::dropIfExists('re_contact_clicks');
    }
};
