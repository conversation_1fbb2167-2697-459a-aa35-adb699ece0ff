<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        if (! Schema::hasColumn('re_projects', 'district_id')) {
            Schema::table('re_projects', function (Blueprint $table): void {
                $table->foreignId('district_id')->nullable()->after('city_id');
                $table->index('district_id');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasColumn('re_projects', 'district_id')) {
            Schema::table('re_projects', function (Blueprint $table): void {
                $table->dropIndex(['district_id']);
                $table->dropColumn('district_id');
            });
        }
    }
};
