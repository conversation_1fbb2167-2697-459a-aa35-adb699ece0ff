<?php

namespace Xmetr\Location\Models;

use Xmetr\Base\Casts\SafeContent;
use Xmetr\Base\Enums\BaseStatusEnum;
use Xmetr\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Xmetr\RealEstate\Models\Project;
use Xmetr\RealEstate\Models\Property;

class District extends BaseModel
{
    protected $table = 'districts';

    protected $fillable = [
        'name',
        'city_id',
        'state_id',
        'country_id',
        'image',
        'order',
        'is_default',
        'status',
        'district_description',
        'amenities_rating',
        'amenities_comment',
        'transport_rating',
        'transport_comment',
        'safety_rating',
        'safety_comment',
        'green_spaces_rating',
        'green_spaces_comment',
        'noise_rating',
        'noise_comment',
        'rent_rating',
        'rent_comment',
        'atmosphere_rating',
        'atmosphere_comment',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'district_description' => SafeContent::class,
        'is_default' => 'bool',
        'order' => 'int',
        'amenities_rating' => 'float',
        'amenities_comment' => SafeContent::class,
        'transport_rating' => 'float',
        'transport_comment' => SafeContent::class,
        'safety_rating' => 'float',
        'safety_comment' => SafeContent::class,
        'green_spaces_rating' => 'float',
        'green_spaces_comment' => SafeContent::class,
        'noise_rating' => 'float',
        'noise_comment' => SafeContent::class,
        'rent_rating' => 'float',
        'rent_comment' => SafeContent::class,
        'atmosphere_rating' => 'float',
        'atmosphere_comment' => SafeContent::class,
    ];

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class)->withDefault();
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class)->withDefault();
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class)->withDefault();
    }

    public function properties()
    {
        return $this->hasMany(Property::class, 'district_id', 'id');
    }

    public function projects()
    {
        return $this->hasMany(Project::class, 'district_id', 'id');
    }

    public function getAverageRatingAttribute(): float
    {
        $ratings = [
            $this->amenities_rating,
            $this->transport_rating,
            $this->safety_rating,
            $this->green_spaces_rating,
            $this->noise_rating,
            $this->rent_rating,
            $this->atmosphere_rating,
        ];

        $validRatings = array_filter($ratings, function ($rating) {
            return $rating !== null && $rating > 0;
        });

        if (empty($validRatings)) {
            return 0.0;
        }

        return round(array_sum($validRatings) / count($validRatings), 2);
    }

    public static function getRatingCategories(): array
    {
        return [
            'amenities' => [
                'label' => trans('plugins/location::district.amenities_label'),
                'key' => 'amenities',
            ],
            'transport' => [
                'label' => trans('plugins/location::district.transport_label'),
                'key' => 'transport',
            ],
            'safety' => [
                'label' => trans('plugins/location::district.safety_label'),
                'key' => 'safety',
            ],
            'green_spaces' => [
                'label' => trans('plugins/location::district.green_spaces_label'),
                'key' => 'green_spaces',
            ],
            'noise' => [
                'label' => trans('plugins/location::district.noise_label'),
                'key' => 'noise',
            ],
            'rent' => [
                'label' => trans('plugins/location::district.rent_label'),
                'key' => 'rent',
            ],
            'atmosphere' => [
                'label' => trans('plugins/location::district.atmosphere_label'),
                'key' => 'atmosphere',
            ],
        ];
    }

    public function getRatingWithDetails(string $category): array
    {
        $categories = self::getRatingCategories();

        if (!isset($categories[$category])) {
            return [];
        }

        $label = $categories[$category]['label'];

        // Extract icon and text from combined label
        $icon = '';
        $labelText = $label;
        if (preg_match('/^(\p{So}+)\s*(.*)$/u', $label, $matches)) {
            $icon = $matches[1];
            $labelText = $matches[2];
        }

        return [
            'icon' => $icon,
            'label' => $labelText,
            'full_label' => $label,
            'rating' => $this->{$category . '_rating'} ?? 0,
            'comment' => $this->{$category . '_comment'} ?? '',
        ];
    }

    public function getAllRatingsWithDetails(): array
    {
        $result = [];
        foreach (array_keys(self::getRatingCategories()) as $category) {
            $result[$category] = $this->getRatingWithDetails($category);
        }
        return $result;
    }
}
