<?php

namespace Xmetr\RealEstate\Models;

use Xmetr\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Carbon\Carbon;

class ContactClick extends BaseModel
{
    protected $table = 're_contact_clicks';

    protected $fillable = [
        'clickable_id',
        'clickable_type',
        'contact_type',
        'click_date',
        'clicks_count',
    ];

    protected $casts = [
        'click_date' => 'date',
        'clicks_count' => 'integer',
    ];

    public function clickable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @deprecated Use clickable() instead
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'clickable_id')->where('clickable_type', Property::class);
    }

    /**
     * Increment clicks for a clickable entity contact type on a specific date
     */
    public static function incrementClicks($clickable, string $contactType, ?Carbon $date = null): void
    {
        $date = $date ?: Carbon::today();

        $record = static::firstOrCreate(
            [
                'clickable_id' => $clickable->getKey(),
                'clickable_type' => get_class($clickable),
                'contact_type' => $contactType,
                'click_date' => $date->format('Y-m-d'),
            ],
            [
                'clicks_count' => 0,
            ]
        );

        $record->increment('clicks_count');
    }

    /**
     * @deprecated Use incrementClicks($clickable, $contactType, $date) instead
     */
    public static function incrementClicksForProperty(int $propertyId, string $contactType, ?Carbon $date = null): void
    {
        $property = Property::find($propertyId);
        if ($property) {
            static::incrementClicks($property, $contactType, $date);
        }
    }

    /**
     * Get clicks count for a clickable entity contact type on a specific date
     */
    public static function getClicksForDate($clickable, string $contactType, ?Carbon $date = null): int
    {
        $date = $date ?: Carbon::today();

        $record = static::where('clickable_id', $clickable->getKey())
            ->where('clickable_type', get_class($clickable))
            ->where('contact_type', $contactType)
            ->where('click_date', $date->format('Y-m-d'))
            ->first();

        return $record ? $record->clicks_count : 0;
    }

    /**
     * @deprecated Use getClicksForDate($clickable, $contactType, $date) instead
     */
    public static function getClicksForDateForProperty(int $propertyId, string $contactType, ?Carbon $date = null): int
    {
        $property = Property::find($propertyId);
        if ($property) {
            return static::getClicksForDate($property, $contactType, $date);
        }
        return 0;
    }

    /**
     * Get total clicks count for a clickable entity contact type
     */
    public static function getTotalClicks($clickable, string $contactType): int
    {
        return static::where('clickable_id', $clickable->getKey())
            ->where('clickable_type', get_class($clickable))
            ->where('contact_type', $contactType)
            ->sum('clicks_count');
    }

    /**
     * @deprecated Use getTotalClicks($clickable, $contactType) instead
     */
    public static function getTotalClicksForProperty(int $propertyId, string $contactType): int
    {
        $property = Property::find($propertyId);
        if ($property) {
            return static::getTotalClicks($property, $contactType);
        }
        return 0;
    }

    /**
     * Get clicks count for a clickable entity contact type within a date range
     */
    public static function getClicksForDateRange($clickable, string $contactType, Carbon $startDate, Carbon $endDate): int
    {
        return static::where('clickable_id', $clickable->getKey())
            ->where('clickable_type', get_class($clickable))
            ->where('contact_type', $contactType)
            ->whereBetween('click_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('clicks_count');
    }

    /**
     * @deprecated Use getClicksForDateRange($clickable, $contactType, $startDate, $endDate) instead
     */
    public static function getClicksForDateRangeForProperty(int $propertyId, string $contactType, Carbon $startDate, Carbon $endDate): int
    {
        $property = Property::find($propertyId);
        if ($property) {
            return static::getClicksForDateRange($property, $contactType, $startDate, $endDate);
        }
        return 0;
    }
}
