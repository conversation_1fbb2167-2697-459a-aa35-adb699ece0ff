<?php

namespace Xmetr\RealEstate\Models;

use Exception;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Support\Str;
use Xmetr\Base\Supports\Avatar;
use Xmetr\Base\Models\BaseModel;
use Xmetr\Media\Facades\RvMedia;
use Laravel\Sanctum\HasApiTokens;
use Xmetr\Base\Casts\SafeContent;
use Xmetr\Media\Models\MediaFile;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Notifications\Notifiable;
use Xmetr\RealEstate\Enums\ReviewStatusEnum;
use Xmetr\RealEstate\Enums\AccountTypeEnum;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Models\SpokenLanguage;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Xmetr\RealEstate\Notifications\ConfirmEmailNotification;
use Xmetr\RealEstate\Notifications\ResetPasswordNotification;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;

class Account extends BaseModel implements
    AuthenticatableContract,
    AuthorizableContract,
    CanResetPasswordContract
{
    use Authenticatable;
    use Authorizable;
    use CanResetPassword;
    use MustVerifyEmail;
    use HasApiTokens;
    use Notifiable;

    protected $table = 're_accounts';

    protected $fillable = [
        'first_name',
        'last_name',
        'username',
        'email',
        'password',
        'avatar_id',
        'dob',
        'phone',
        'description',
        'gender',
        'company',
        'country_id',
        'state_id',
        'city_id',
        'is_featured',
        'is_public_profile',
        'account_type',
        'is_verified',
        'last_login',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'dob' => 'datetime',
        'package_start_date' => 'datetime',
        'package_end_date' => 'datetime',
        'is_featured' => 'boolean',
        'is_public_profile' => 'boolean',
        'is_verified' => 'boolean',
        'first_name' => SafeContent::class,
        'last_name' => SafeContent::class,
        'username' => SafeContent::class,
        'phone' => SafeContent::class,
        'description' => SafeContent::class,
        'company' => SafeContent::class,
        'password' => 'hashed',
        'approved_at' => 'datetime',
        'last_login' => 'datetime',
        'account_type' => AccountTypeEnum::class,
    ];

    public function activityLogs(): HasMany
    {
        return $this->hasMany(AccountActivityLog::class, 'account_id');
    }

    protected static function booted(): void
    {
        static::deleting(function (Account $account): void {
            $account->activityLogs()->delete();
            $account->transactions()->delete();
            $account->reviews()->delete();
            $account->packages()->detach();
            $account->spokenLanguages()->detach();

            $folder = Storage::path($account->upload_folder);
            if (File::isDirectory($folder) && Str::endsWith($account->upload_folder, '/' . $account->username)) {
                File::deleteDirectory($folder);
            }

            $account->reviews()->delete();
            $account->invoices()->delete();
        });
    }

    public static function generateUsername(string $firstName, ?string $lastName = null): string
    {
        $i = 0;

        do {
            $username = Str::slug($firstName . ' ' . $lastName) . ($i ?: '');
            $i++;
        } while (static::query()->where('username', $username)->exists());

        return $username;
    }

    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new ResetPasswordNotification($token));
    }

    public function sendEmailVerificationNotification(): void
    {
        $this->notify(new ConfirmEmailNotification());
    }

    public function avatar(): BelongsTo
    {
        return $this->belongsTo(MediaFile::class)->withDefault();
    }

    protected function firstName(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => ucfirst($value),
            set: fn ($value) => ucfirst($value),
        );
    }

    protected function lastName(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => ucfirst($value),
            set: fn ($value) => ucfirst($value),
        );
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->first_name . ' ' . $this->last_name,
        );
    }

    protected function avatarUrl(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->avatar->url) {
                    return RvMedia::url($this->avatar->url);
                }

                try {
                    // return (new Avatar())->create($this->name)->toBase64();
                    return RvMedia::url('accounts/default.png');
                } catch (Exception) {
                    return RvMedia::getDefaultImage();
                }
            },
        );
    }

    /**
     * @deprecated since v2.22
     */
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->name
        );
    }

    protected function credits(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                if (! RealEstateHelper::isEnabledCreditsSystem()) {
                    return 0;
                }

                return $value ?: 0;
            }
        );
    }

    public function properties(): MorphMany
    {
        return $this->morphMany(Property::class, 'author');
    }

    public function projects(): MorphMany
    {
        return $this->morphMany(Project::class, 'author');
    }

    public function canPost(): bool
    {
        return ! RealEstateHelper::isEnabledCreditsSystem() || $this->credits > 0;
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'account_id');
    }

    public function packages(): BelongsToMany
    {
        return $this->belongsToMany(Package::class, 're_account_packages', 'account_id', 'package_id');
    }

    protected function uploadFolder(): Attribute
    {
        return Attribute::make(
            get: function () {
                $folder = $this->username ? 'accounts/' . $this->username : 'accounts';

                return apply_filters('real_estate_account_upload_folder', $folder, $this);
            }
        );
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    public function canReview(Project|Property $model): bool
    {
        if (! auth('account')->check()) {
            return false;
        }

        return ! $model
            ->reviews()
            ->whereNot('status', ReviewStatusEnum::REJECTED)
            ->where('account_id', auth('account')->id())
            ->exists();
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function getWithXmetrAttribute()
    {
        return $this->created_at->diffForHumans(Carbon::now(), [
            'parts' => 1,
            'syntax' => CarbonInterface::DIFF_ABSOLUTE,
        ]) . ' ' . __('with XMetr');
    }

    // the function bellow to get the total number of properties count with text
    public function getPropertiesCountAttribute()
    {
        return $this->properties()->count();
    }

    public function spokenLanguages(): BelongsToMany
    {
        return $this->belongsToMany(SpokenLanguage::class, 're_account_spoken_languages', 'account_id', 'spoken_language_id');
    }
}
