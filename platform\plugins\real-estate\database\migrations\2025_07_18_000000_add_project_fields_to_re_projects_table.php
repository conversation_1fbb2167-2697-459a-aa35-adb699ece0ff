<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('re_projects', function (Blueprint $table): void {
            $table->integer('parking')->nullable()->after('number_flat');
            $table->year('year_built')->nullable()->after('parking');
            $table->string('build_class', 30)->nullable()->after('year_built');
        });
    }

    public function down(): void
    {
        Schema::table('re_projects', function (Blueprint $table): void {
            $table->dropColumn(['parking', 'year_built', 'build_class']);
        });
    }
};
