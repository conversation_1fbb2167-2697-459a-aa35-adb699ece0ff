{"version": 3, "file": "/vendor/core/plugins/newsletter/js/newsletter.js", "mappings": ";;;;;AAAAA,CAAC,CAAC,YAAM;EACJ,IAAMC,gBAAgB,GAAGD,CAAC,CAAC,mBAAmB,CAAC;EAE/C,IAAME,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;IAC5B,IAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC;IACvBD,IAAI,CAACE,OAAO,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAAC;IACnCK,QAAQ,CAACC,MAAM,kCAAAC,MAAA,CAAkCN,IAAI,CAACO,WAAW,CAAC,CAAC,aAAU;EACjF,CAAC;EAED,IAAIV,gBAAgB,CAACW,MAAM,GAAG,CAAC,EAAE;IAC7B,IAAIJ,QAAQ,CAACC,MAAM,CAACI,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE;MACtDC,UAAU,CAAC,YAAM;QACbb,gBAAgB,CAACc,KAAK,CAAC,MAAM,CAAC;MAClC,CAAC,EAAEd,gBAAgB,CAACe,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;IAC7C;IAEAf,gBAAgB,CACXgB,EAAE,CAAC,eAAe,EAAE,YAAM;MACvB,IAAMC,MAAM,GAAGjB,gBAAgB,CAACkB,IAAI,CAAC,eAAe,CAAC;MAErDD,MAAM,CAACE,GAAG,CAAC,YAAY,EAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACtB,CAAC,CAACuB,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC,GAAGN,MAAM,CAACM,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAE,CAAC;IAC3F,CAAC,CAAC,CACDP,EAAE,CAAC,eAAe,EAAE,YAAM;MACvB,IAAMQ,QAAQ,GAAGxB,gBAAgB,CAACkB,IAAI,CAAC,MAAM,CAAC,CAACA,IAAI,CAAC,+BAA+B,CAAC;MAEpF,IAAIM,QAAQ,CAACC,EAAE,CAAC,UAAU,CAAC,EAAE;QACzBxB,aAAa,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAC;MAC3C,CAAC,MAAM;QACHA,aAAa,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAC;MAClC;IACJ,CAAC,CAAC;IAENM,QAAQ,CAACmB,gBAAgB,CAAC,uBAAuB,EAAE;MAAA,OAAMzB,aAAa,CAAC,CAAC;IAAA,EAAC;IAEzE,IAAI0B,SAAS,GAAG,SAAZA,SAASA,CAAaC,OAAO,EAAE;MAC/B7B,CAAC,CAAC,2BAA2B,CAAC,CAAC8B,IAAI,CAACD,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,IAAIC,WAAW,GAAG,SAAdA,WAAWA,CAAaH,OAAO,EAAE;MACjC7B,CAAC,CAAC,6BAA6B,CAAC,CAAC8B,IAAI,CAACD,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,IAAIE,WAAW,GAAG,SAAdA,WAAWA,CAAajB,IAAI,EAAE;MAC9B,IAAI,OAAOA,IAAI,CAACkB,MAAM,KAAK,WAAW,IAAIlB,IAAI,CAACkB,MAAM,CAACtB,MAAM,EAAE;QAC1DuB,qBAAqB,CAACnB,IAAI,CAACkB,MAAM,CAAC;MACtC,CAAC,MAAM;QACH,IAAI,OAAOlB,IAAI,CAACoB,YAAY,KAAK,WAAW,EAAE;UAC1C,IAAI,OAAOpB,IAAI,CAACoB,YAAY,CAACF,MAAM,KAAK,WAAW,EAAE;YACjD,IAAIlB,IAAI,CAACqB,MAAM,KAAK,GAAG,EAAE;cACrBF,qBAAqB,CAACnB,IAAI,CAACoB,YAAY,CAACF,MAAM,CAAC;YACnD;UACJ,CAAC,MAAM,IAAI,OAAOlB,IAAI,CAACoB,YAAY,CAACP,OAAO,KAAK,WAAW,EAAE;YACzDD,SAAS,CAACZ,IAAI,CAACoB,YAAY,CAACP,OAAO,CAAC;UACxC,CAAC,MAAM;YACH7B,CAAC,CAACsC,IAAI,CAACtB,IAAI,CAACoB,YAAY,EAAE,UAACG,KAAK,EAAEC,EAAE,EAAK;cACrCxC,CAAC,CAACsC,IAAI,CAACE,EAAE,EAAE,UAACC,GAAG,EAAEC,IAAI,EAAK;gBACtBd,SAAS,CAACc,IAAI,CAAC;cACnB,CAAC,CAAC;YACN,CAAC,CAAC;UACN;QACJ,CAAC,MAAM;UACHd,SAAS,CAACZ,IAAI,CAAC2B,UAAU,CAAC;QAC9B;MACJ;IACJ,CAAC;IAED,IAAIR,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAaD,MAAM,EAAE;MAC1C,IAAIL,OAAO,GAAG,EAAE;MAChB7B,CAAC,CAACsC,IAAI,CAACJ,MAAM,EAAE,UAACK,KAAK,EAAEG,IAAI,EAAK;QAC5B,IAAIb,OAAO,KAAK,EAAE,EAAE;UAChBA,OAAO,IAAI,QAAQ;QACvB;QACAA,OAAO,IAAIa,IAAI;MACnB,CAAC,CAAC;MACFd,SAAS,CAACC,OAAO,CAAC;IACtB,CAAC;IAED7B,CAAC,CAACQ,QAAQ,CAAC,CAACS,EAAE,CAAC,QAAQ,EAAE,+BAA+B,EAAE,UAAC2B,CAAC,EAAK;MAC7DA,CAAC,CAACC,cAAc,CAAC,CAAC;MAElB,IAAMC,KAAK,GAAG9C,CAAC,CAAC4C,CAAC,CAACG,aAAa,CAAC;MAChC,IAAMC,OAAO,GAAGF,KAAK,CAAC3B,IAAI,CAAC,qBAAqB,CAAC;MAEjDnB,CAAC,CAAC,6BAA6B,CAAC,CAAC8B,IAAI,CAAC,EAAE,CAAC,CAACmB,IAAI,CAAC,CAAC;MAChDjD,CAAC,CAAC,2BAA2B,CAAC,CAAC8B,IAAI,CAAC,EAAE,CAAC,CAACmB,IAAI,CAAC,CAAC;MAE9CjD,CAAC,CAACkD,IAAI,CAAC;QACHC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,KAAK;QACZC,GAAG,EAAEP,KAAK,CAACQ,IAAI,CAAC,QAAQ,CAAC;QACzBtC,IAAI,EAAE,IAAIuC,QAAQ,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5BU,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,KAAK;QAClBC,UAAU,EAAE,SAAAA,WAAA;UAAA,OAAMV,OAAO,CAACM,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAACK,QAAQ,CAAC,aAAa,CAAC;QAAA;QACxEC,OAAO,EAAE,SAAAA,QAAAC,IAAA,EAAwB;UAAA,IAArBC,KAAK,GAAAD,IAAA,CAALC,KAAK;YAAEjC,OAAO,GAAAgC,IAAA,CAAPhC,OAAO;UACtB,IAAIiC,KAAK,EAAE;YACPlC,SAAS,CAACC,OAAO,CAAC;YAElB;UACJ;UAEAiB,KAAK,CAAC3B,IAAI,CAAC,qBAAqB,CAAC,CAAC4C,GAAG,CAAC,EAAE,CAAC;UAEzC/B,WAAW,CAACH,OAAO,CAAC;UAEpBrB,QAAQ,CAACwD,aAAa,CAAC,IAAIC,WAAW,CAAC,uBAAuB,CAAC,CAAC;UAEhEnD,UAAU,CAAC,YAAM;YACbb,gBAAgB,CAACc,KAAK,CAAC,MAAM,CAAC;UAClC,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC;QACD+C,KAAK,EAAE,SAAAA,MAACA,MAAK;UAAA,OAAK7B,WAAW,CAAC6B,MAAK,CAAC;QAAA;QACpCI,QAAQ,EAAE,SAAAA,SAAA,EAAM;UACZ,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAE;YACzCA,gBAAgB,CAAC,CAAC;UACtB;UAEAnB,OAAO,CAACM,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAACc,WAAW,CAAC,aAAa,CAAC;QAC9D;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ,CAAC,CAAC,C", "sources": ["webpack:///./platform/plugins/newsletter/resources/js/newsletter.js"], "sourcesContent": ["$(() => {\n    const $newsletterPopup = $('#newsletter-popup')\n\n    const dontShowAgain = (time) => {\n        const date = new Date()\n        date.setTime(date.getTime() + time)\n        document.cookie = `newsletter_popup=1; expires=${date.toUTCString()}; path=/`\n    }\n\n    if ($newsletterPopup.length > 0) {\n        if (document.cookie.indexOf('newsletter_popup=1') === -1) {\n            setTimeout(() => {\n                $newsletterPopup.modal('show')\n            }, $newsletterPopup.data('delay') * 1000)\n        }\n\n        $newsletterPopup\n            .on('show.bs.modal', () => {\n                const dialog = $newsletterPopup.find('.modal-dialog')\n\n                dialog.css('margin-top', (Math.max(0, ($(window).height() - dialog.height()) / 2) / 2))\n            })\n            .on('hide.bs.modal', () => {\n                const checkbox = $newsletterPopup.find('form').find('input[name=\"dont_show_again\"]')\n\n                if (checkbox.is(':checked')) {\n                    dontShowAgain(3 * 24 * 60 * 60 * 1000) // 1 day\n                } else {\n                    dontShowAgain(60 * 60 * 1000) // 1 hour\n                }\n            })\n\n        document.addEventListener('newsletter.subscribed', () => dontShowAgain())\n\n        let showError = function (message) {\n            $('.newsletter-error-message').html(message).show()\n        }\n\n        let showSuccess = function (message) {\n            $('.newsletter-success-message').html(message).show()\n        }\n\n        let handleError = function (data) {\n            if (typeof data.errors !== 'undefined' && data.errors.length) {\n                handleValidationError(data.errors)\n            } else {\n                if (typeof data.responseJSON !== 'undefined') {\n                    if (typeof data.responseJSON.errors !== 'undefined') {\n                        if (data.status === 422) {\n                            handleValidationError(data.responseJSON.errors)\n                        }\n                    } else if (typeof data.responseJSON.message !== 'undefined') {\n                        showError(data.responseJSON.message)\n                    } else {\n                        $.each(data.responseJSON, (index, el) => {\n                            $.each(el, (key, item) => {\n                                showError(item)\n                            })\n                        })\n                    }\n                } else {\n                    showError(data.statusText)\n                }\n            }\n        }\n\n        let handleValidationError = function (errors) {\n            let message = ''\n            $.each(errors, (index, item) => {\n                if (message !== '') {\n                    message += '<br />'\n                }\n                message += item\n            })\n            showError(message)\n        }\n\n        $(document).on('submit', 'form.bb-newsletter-popup-form', (e) => {\n            e.preventDefault()\n\n            const $form = $(e.currentTarget)\n            const $button = $form.find('button[type=submit]')\n\n            $('.newsletter-success-message').html('').hide()\n            $('.newsletter-error-message').html('').hide()\n\n            $.ajax({\n                type: 'POST',\n                cache: false,\n                url: $form.prop('action'),\n                data: new FormData($form[0]),\n                contentType: false,\n                processData: false,\n                beforeSend: () => $button.prop('disabled', true).addClass('btn-loading'),\n                success: ({ error, message }) => {\n                    if (error) {\n                        showError(message)\n\n                        return\n                    }\n\n                    $form.find('input[name=\"email\"]').val('')\n\n                    showSuccess(message)\n\n                    document.dispatchEvent(new CustomEvent('newsletter.subscribed'))\n\n                    setTimeout(() => {\n                        $newsletterPopup.modal('hide')\n                    }, 5000)\n                },\n                error: (error) => handleError(error),\n                complete: () => {\n                    if (typeof refreshRecaptcha !== 'undefined') {\n                        refreshRecaptcha()\n                    }\n\n                    $button.prop('disabled', false).removeClass('btn-loading')\n                },\n            })\n        })\n    }\n})\n"], "names": ["$", "$newsletterPopup", "dontShowAgain", "time", "date", "Date", "setTime", "getTime", "document", "cookie", "concat", "toUTCString", "length", "indexOf", "setTimeout", "modal", "data", "on", "dialog", "find", "css", "Math", "max", "window", "height", "checkbox", "is", "addEventListener", "showError", "message", "html", "show", "showSuccess", "handleError", "errors", "handleValidationError", "responseJSON", "status", "each", "index", "el", "key", "item", "statusText", "e", "preventDefault", "$form", "currentTarget", "$button", "hide", "ajax", "type", "cache", "url", "prop", "FormData", "contentType", "processData", "beforeSend", "addClass", "success", "_ref", "error", "val", "dispatchEvent", "CustomEvent", "complete", "refreshRecaptcha", "removeClass"], "sourceRoot": ""}