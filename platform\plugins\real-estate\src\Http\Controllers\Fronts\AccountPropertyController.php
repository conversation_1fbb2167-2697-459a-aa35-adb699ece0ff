<?php

namespace Xmetr\RealEstate\Http\Controllers\Fronts;

use Xmetr\Base\Events\DeletedContentEvent;
use Xmetr\Base\Facades\EmailHandler;
use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Optimize\Facades\OptimizerHelper;
use Xmetr\RealEstate\Enums\ModerationStatusEnum;
use Xmetr\RealEstate\Enums\PropertyStatusEnum;
use Xmetr\RealEstate\Enums\PropertyTypeEnum;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\AccountPropertyForm;
use Xmetr\RealEstate\Http\Requests\AccountPropertyRequest;
use Xmetr\RealEstate\Models\Account;
use Xmetr\RealEstate\Models\AccountActivityLog;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Repositories\Interfaces\AccountActivityLogInterface;
use Xmetr\RealEstate\Repositories\Interfaces\AccountInterface;
use Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface;
use Xmetr\RealEstate\Services\SaveFacilitiesService;
use Xmetr\RealEstate\Services\SavePropertyCustomFieldService;
use Xmetr\RealEstate\Services\StorePropertyCategoryService;
use Xmetr\RealEstate\Tables\AccountPropertyTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Xmetr\RealEstate\Events\PropertyCreated;

class AccountPropertyController extends BaseController
{
    public function __construct(
        protected AccountInterface $accountRepository,
        protected PropertyInterface $propertyRepository,
        protected AccountActivityLogInterface $activityLogRepository
    ) {
        OptimizerHelper::disable();
    }

    public function index(AccountPropertyTable $propertyTable)
    {
        $this->pageTitle(trans('plugins/real-estate::account-property.properties'));

        // return $propertyTable->render('plugins/real-estate::account.table.base');

        // Define the number of items per page
        $perPage = 10; // You can adjust this as needed

        // Query with pagination
        $not_rented_properties= Property::query()
            ->where([
                'author_id' => auth('account')->id(),
                'author_type' => Account::class,
            ])
            // ->orderBy('moderation_status', ModerationStatusEnum::PENDING)
            ->where('status', '!=', PropertyStatusEnum::RENTED)
            ->orderByRaw("CASE
                        WHEN moderation_status = ? THEN 0
                        ELSE 1
                    END", [ModerationStatusEnum::PENDING])
            ->orderBy('created_at', 'desc') // Secondary sorting if needed
            ->get();

       $rented_properties= Property::query()
            ->where([
                'author_id' => auth('account')->id(),
                'author_type' => Account::class,
                'status' => PropertyStatusEnum::RENTED
            ])
            ->get();
        // $properties = Property::query()
        // ->where([
        //     'author_id' => auth('account')->id(),
        //     'author_type' => Account::class
        // ])
        // ->get();

        return view('plugins/real-estate::account.properties.list', compact('not_rented_properties', 'rented_properties'));
    }

    public function create()
    {
        if (! auth('account')->user()->canPost()) {
            return redirect()->back()->with(['error_msg' => trans('plugins/real-estate::package.add_credit_alert')]);
        }

        $this->pageTitle(trans('plugins/real-estate::account-property.write_property'));

        return AccountPropertyForm::create()->renderForm();
    }

    public function store(
        AccountPropertyRequest $request,
        StorePropertyCategoryService $propertyCategoryService,
        SaveFacilitiesService $saveFacilitiesService,
        SavePropertyCustomFieldService $savePropertyCustomFieldService
    ) {
        if (! auth('account')->user()->canPost()) {
            return redirect()->back()->with(['error_msg' => trans('plugins/real-estate::package.add_credit_alert')]);
        }

        $propertyForm = AccountPropertyForm::create()->setRequest($request);

        $propertyForm->saving(function (AccountPropertyForm $form) use (
            $propertyCategoryService,
            $saveFacilitiesService,
            $savePropertyCustomFieldService
        ): void {
            $request = $form->getRequest();

            /**
             * @var Property $property
             */
            $property = $form->getModel();

            $property->fill(array_merge($this->processRequestData($request), [
                'author_id' => auth('account')->id(),
                'author_type' => Account::class,
            ]));

            $property->expire_date = Carbon::now()->addDays(RealEstateHelper::propertyExpiredDays());

            if (setting('enable_post_approval', 1) == 0) {
                $property->moderation_status = ModerationStatusEnum::APPROVED;
            }

            $property->save();

            if (RealEstateHelper::isEnabledCustomFields()) {
                $savePropertyCustomFieldService->execute($property, $request->input('custom_fields', []));
            }

            $property->features()->sync($request->input('features', []));

            // Handle suitable_for as array field instead of relationship
            $property->suitable_for = $request->input('suitable_for', []);
            $property->save();

            $saveFacilitiesService->execute($property, $request->input('facilities', []));

            $propertyCategoryService->execute($request, $property);

            $form->fireModelEvents($property);

            event(new PropertyCreated($property));

            AccountActivityLog::query()->create([
                'action' => 'create_property',
                'reference_name' => $property->name,
                'reference_url' => route('public.account.properties.edit', $property->id),
            ]);

            if (RealEstateHelper::isEnabledCreditsSystem()) {
                $account = Account::query()->findOrFail(auth('account')->id());
                $account->credits--;
                $account->save();
            }

            EmailHandler::setModule(REAL_ESTATE_MODULE_SCREEN_NAME)
                ->setVariableValues([
                    'post_name' => $property->name,
                    'post_url' => route('property.edit', $property->id),
                    'post_author' => $property->author->name,
                ])
                ->sendUsingTemplate('new-pending-property');
        });

        return $this
            ->httpResponse()
            // ->setPreviousUrl(route('public.account.properties.index'))
            ->setPreviousUrl(route('public.single', 'listing-submitted'))
            // ->setNextUrl(route('public.account.properties.edit', $propertyForm->getModel()->getKey()))
            ->setNextUrl(route('public.single', 'listing-submitted'))
            // ->setMessage(trans('core/base::notices.create_success_message'))
            ;
    }

    public function edit(int|string $id)
    {
        $property = Property::query()
            ->where([
                'id' => $id,
                'author_id' => auth('account')->id(),
                'author_type' => Account::class,
            ])
            ->firstOrFail();

        $this->pageTitle(trans('plugins/real-estate::property.edit') . ' "' . $property->name . '"');

        return AccountPropertyForm::createFromModel($property)
            ->renderForm();
    }

    public function update(
        int|string $id,
        AccountPropertyRequest $request,
        StorePropertyCategoryService $propertyCategoryService,
        SaveFacilitiesService $saveFacilitiesService,
        SavePropertyCustomFieldService $savePropertyCustomFieldService
    ) {
        $property = Property::query()
            ->where([
                'id' => $id,
                'author_id' => auth('account')->id(),
                'author_type' => Account::class,
            ])
            ->firstOrFail();

        $propertyForm = AccountPropertyForm::createFromModel($property)->setRequest($request);

        $propertyForm->saving(function (AccountPropertyForm $form) use (
            $propertyCategoryService,
            $saveFacilitiesService,
            $savePropertyCustomFieldService
        ): void {
            $request = $form->getRequest();

            /**
             * @var Property $property
             */
            $property = $form->getModel();

            $property->fill($this->processRequestData($request));

            $property->save();

            $form->fireModelEvents($property);

            if (RealEstateHelper::isEnabledCustomFields()) {
                $savePropertyCustomFieldService->execute($property, $request->input('custom_fields', []));
            }

            $property->features()->sync($request->input('features', []));

            // Handle suitable_for as array field instead of relationship
            $property->suitable_for = $request->input('suitable_for', []);
            $property->save();

            $saveFacilitiesService->execute($property, $request->input('facilities', []));

            $propertyCategoryService->execute($request, $property);

            AccountActivityLog::query()->create([
                'action' => 'update_property',
                'reference_name' => $property->name,
                'reference_url' => route('public.account.properties.edit', $property->id),
            ]);
        });

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('public.account.properties.index'))
            ->setNextUrl(route('public.account.properties.edit', $property->id))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    protected function processRequestData(Request $request): array
    {
        $shortcodeCompiler = shortcode()->getCompiler();

        $request->merge([
            'content' => $shortcodeCompiler->strip($request->input('content'), $shortcodeCompiler->whitelistShortcodes()),
        ]);

        $except = [
            'is_featured',
            'author_id',
            'author_type',
            'expire_date',
            'never_expired',
            'moderation_status',
        ];

        foreach ($except as $item) {
            $request->request->remove($item);
        }

        $data = $request->input();

        // Handle video thumbnail clearing when video is removed
        if (empty($data['video'])) {
            $data['video_thumbnail'] = null;
        }

        return $data;
    }

    public function destroy(int|string $id, Request $request)
    {
        $property = $this->propertyRepository->getFirstBy([
            'id' => $id,
            'author_id' => auth('account')->id(),
            'author_type' => Account::class,
        ]);

        abort_unless($property, 404);

        $property->delete();

        event(new DeletedContentEvent(PROPERTY_MODULE_SCREEN_NAME, $request, $property));

        AccountActivityLog::query()->create([
            'action' => 'delete_property',
            'reference_name' => $property->name,
        ]);

        return $this
            ->httpResponse()
            ->setMessage(__('Delete property successfully!'));
    }

    public function renew(int|string $id)
    {
        $property = Property::query()->findOrFail($id);

        $account = auth('account')->user();

        if (RealEstateHelper::isEnabledCreditsSystem() && $account->credits < 1) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__("You don't have enough credit to renew this property!"));
        }

        $property->expire_date = $property->expire_date->addDays(RealEstateHelper::propertyExpiredDays());
        $property->save();

        if (RealEstateHelper::isEnabledCreditsSystem()) {
            $account->credits--;
            $account->save();
        }

        return $this
            ->httpResponse()
            ->setMessage(__('Renew property successfully'));
    }

    public function rentProperty(int|string $id)
    {
        $property = Property::query()->findOrFail($id);
        $property->status = PropertyStatusEnum::RENTED;
        $property->save();

        return $this
            ->httpResponse()
            ->setMessage(__('Property rented successfully'));
    }
    public function recoverProperty(int|string $id)
    {
        $property = Property::query()->findOrFail($id);
        $property->status = PropertyStatusEnum::RENTING;
        $property->save();

        return $this
            ->httpResponse()
            ->setMessage(__('Property recovered successfully'));
    }
}
