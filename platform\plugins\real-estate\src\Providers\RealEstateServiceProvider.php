<?php

namespace Xmetr\RealEstate\Providers;

use Xmetr\Api\Facades\ApiHelper;
use Xmetr\Base\Facades\DashboardMenu;
use Xmetr\Base\Facades\EmailHandler;
use Xmetr\Base\Facades\MacroableModels;
use Xmetr\Base\Facades\PanelSectionManager;
use Xmetr\Base\Supports\DashboardMenu as DashboardMenuSupport;
use Xmetr\Base\Supports\Language as BaseLanguage;
use Xmetr\Base\Traits\LoadAndPublishDataTrait;
use Xmetr\Captcha\Facades\Captcha;
use Xmetr\Language\Facades\Language;
use Xmetr\LanguageAdvanced\Supports\LanguageAdvancedManager;
use Xmetr\Location\Facades\Location;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\State;
use Xmetr\RealEstate\Commands\RenewPropertiesCommand;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\Fronts\Auth\ForgotPasswordForm;
use Xmetr\RealEstate\Forms\Fronts\Auth\LoginForm;
use Xmetr\RealEstate\Forms\Fronts\Auth\RegisterForm;
use Xmetr\RealEstate\Forms\Fronts\Auth\ResetPasswordForm;
use Xmetr\RealEstate\Forms\Fronts\ConsultForm;
use Xmetr\RealEstate\Forms\Fronts\ReviewForm;
use Xmetr\RealEstate\Http\Middleware\RedirectIfAccount;
use Xmetr\RealEstate\Http\Middleware\RedirectIfNotAccount;
use Xmetr\RealEstate\Http\Requests\Fronts\Auth\ForgotPasswordRequest;
use Xmetr\RealEstate\Http\Requests\Fronts\Auth\LoginRequest;
use Xmetr\RealEstate\Http\Requests\Fronts\Auth\RegisterRequest;
use Xmetr\RealEstate\Http\Requests\Fronts\Auth\ResetPasswordRequest;
use Xmetr\RealEstate\Http\Requests\ReviewRequest;
use Xmetr\RealEstate\Http\Requests\SendConsultRequest;
use Xmetr\RealEstate\Models\Account;
use Xmetr\RealEstate\Models\AccountActivityLog;
use Xmetr\RealEstate\Models\Category;
use Xmetr\RealEstate\Models\Consult;
use Xmetr\RealEstate\Models\ConsultCustomField;
use Xmetr\RealEstate\Models\ConsultCustomFieldOption;
use Xmetr\RealEstate\Models\Currency;
use Xmetr\RealEstate\Models\CustomField;
use Xmetr\RealEstate\Models\CustomFieldOption;
use Xmetr\RealEstate\Models\CustomFieldValue;
use Xmetr\RealEstate\Models\Facility;
use Xmetr\RealEstate\Models\Feature;
use Xmetr\RealEstate\Models\Investor;
use Xmetr\RealEstate\Models\Invoice;
use Xmetr\RealEstate\Models\Package;
use Xmetr\RealEstate\Models\Project;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Models\Review;
use Xmetr\RealEstate\Models\SpokenLanguage;

use Xmetr\RealEstate\Models\Transaction;
use Xmetr\RealEstate\PanelSections\SettingRealEstatePanelSetting;
use Xmetr\RealEstate\Repositories\Eloquent\AccountActivityLogRepository;
use Xmetr\RealEstate\Repositories\Eloquent\AccountRepository;
use Xmetr\RealEstate\Repositories\Eloquent\CategoryRepository;
use Xmetr\RealEstate\Repositories\Eloquent\ConsultRepository;
use Xmetr\RealEstate\Repositories\Eloquent\CurrencyRepository;
use Xmetr\RealEstate\Repositories\Eloquent\CustomFieldRepository;
use Xmetr\RealEstate\Repositories\Eloquent\FacilityRepository;
use Xmetr\RealEstate\Repositories\Eloquent\FeatureRepository;
use Xmetr\RealEstate\Repositories\Eloquent\InvestorRepository;
use Xmetr\RealEstate\Repositories\Eloquent\InvoiceRepository;
use Xmetr\RealEstate\Repositories\Eloquent\PackageRepository;
use Xmetr\RealEstate\Repositories\Eloquent\ProjectRepository;
use Xmetr\RealEstate\Repositories\Eloquent\PropertyRepository;
use Xmetr\RealEstate\Repositories\Eloquent\ReviewRepository;
use Xmetr\RealEstate\Repositories\Eloquent\SpokenLanguageRepository;

use Xmetr\RealEstate\Repositories\Eloquent\TransactionRepository;
use Xmetr\RealEstate\Repositories\Interfaces\AccountActivityLogInterface;
use Xmetr\RealEstate\Repositories\Interfaces\AccountInterface;
use Xmetr\RealEstate\Repositories\Interfaces\CategoryInterface;
use Xmetr\RealEstate\Repositories\Interfaces\ConsultInterface;
use Xmetr\RealEstate\Repositories\Interfaces\CurrencyInterface;
use Xmetr\RealEstate\Repositories\Interfaces\CustomFieldInterface;
use Xmetr\RealEstate\Repositories\Interfaces\FacilityInterface;
use Xmetr\RealEstate\Repositories\Interfaces\FeatureInterface;
use Xmetr\RealEstate\Repositories\Interfaces\InvestorInterface;
use Xmetr\RealEstate\Repositories\Interfaces\InvoiceInterface;
use Xmetr\RealEstate\Repositories\Interfaces\PackageInterface;
use Xmetr\RealEstate\Repositories\Interfaces\ProjectInterface;
use Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface;
use Xmetr\RealEstate\Repositories\Interfaces\ReviewInterface;
use Xmetr\RealEstate\Repositories\Interfaces\SpokenLanguageInterface;

use Xmetr\RealEstate\Repositories\Interfaces\TransactionInterface;
use Xmetr\RssFeed\Facades\RssFeed;
use Xmetr\SeoHelper\Facades\SeoHelper;
use Xmetr\Slug\Facades\SlugHelper;
use Xmetr\SocialLogin\Facades\SocialService;
use Xmetr\Theme\Facades\SiteMapManager;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\AliasLoader;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Xmetr\RealEstate\Services\ExchangeRates\ApiLayerExchangeRateService;
use Xmetr\RealEstate\Services\ExchangeRates\ExchangeRateInterface;
use Xmetr\RealEstate\Services\ExchangeRates\OpenExchangeRatesService;

class RealEstateServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->singleton(PropertyInterface::class, function () {
            return new PropertyRepository(new Property());
        });

        $this->app->singleton(ProjectInterface::class, function () {
            return new ProjectRepository(new Project());
        });

        $this->app->singleton(FeatureInterface::class, function () {
            return new FeatureRepository(new Feature());
        });



        $this->app->singleton(SpokenLanguageInterface::class, function () {
            return new SpokenLanguageRepository(new SpokenLanguage());
        });

        $this->app->bind(InvestorInterface::class, function () {
            return new InvestorRepository(new Investor());
        });

        $this->app->bind(CurrencyInterface::class, function () {
            return new CurrencyRepository(new Currency());
        });

        $this->app->bind(ConsultInterface::class, function () {
            return new ConsultRepository(new Consult());
        });

        $this->app->bind(CategoryInterface::class, function () {
            return new CategoryRepository(new Category());
        });

        $this->app->bind(FacilityInterface::class, function () {
            return new FacilityRepository(new Facility());
        });

        $this->app->bind(CustomFieldInterface::class, function () {
            return new CustomFieldRepository(new CustomField());
        });

        $this->app->bind(ReviewInterface::class, function () {
            return new ReviewRepository(new Review());
        });

        $this->app->bind(InvoiceInterface::class, function () {
            return new InvoiceRepository(new Invoice());
        });

        $this->app->bind(AccountInterface::class, function () {
            return new AccountRepository(new Account());
        });

        $this->app->bind(AccountActivityLogInterface::class, function () {
            return new AccountActivityLogRepository(new AccountActivityLog());
        });

        $this->app->bind(PackageInterface::class, function () {
            return new PackageRepository(new Package());
        });

        $this->app->singleton(TransactionInterface::class, function () {
            return new TransactionRepository(new Transaction());
        });

        $this->app->singleton(ExchangeRateInterface::class, function () {
            if (setting('exchange_rate_api_provider') === 'api_layer') {
                return new ApiLayerExchangeRateService();
            }

            return new OpenExchangeRatesService();
        });

        config([
            'auth.guards.account' => [
                'driver' => 'session',
                'provider' => 'accounts',
            ],
            'auth.providers.accounts' => [
                'driver' => 'eloquent',
                'model' => Account::class,
            ],
            'auth.passwords.accounts' => [
                'provider' => 'accounts',
                'table' => 're_account_password_resets',
                'expire' => 60,
            ],
        ]);

        $router = $this->app['router'];

        $router->aliasMiddleware('account', RedirectIfNotAccount::class);
        $router->aliasMiddleware('account.guest', RedirectIfAccount::class);

        $loader = AliasLoader::getInstance();
        $loader->alias('RealEstateHelper', RealEstateHelper::class);
    }

    public function boot(): void
    {
        add_filter(IS_IN_ADMIN_FILTER, [$this, 'setInAdmin'], 128);

        $this->setNamespace('plugins/real-estate')
            ->loadAndPublishConfigurations(['permissions', 'email', 'real-estate', 'general'])
            ->loadMigrations()
            ->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->loadHelpers()
            ->loadRoutes(['web', 'fronts'])
            ->publishAssets();

        $this->app->booted(function (): void {

            if (! setting('real_estate_enable_account_verification', false)) {
                config([
                    'plugins.real-estate.email.templates' => Arr::except(
                        config('plugins.real-estate.email.templates'),
                        ['account-approved', 'account-rejected']
                    ),
                ]);
            }
        });

        SlugHelper::registering(function (): void {
            SlugHelper::registerModule(Property::class, fn () => trans('plugins/real-estate::property.properties'));
            SlugHelper::registerModule(Category::class, fn () => trans('plugins/real-estate::category.property_categories'));
            SlugHelper::registerModule(Project::class, fn () => trans('plugins/real-estate::project.projects'));
            SlugHelper::setPrefix(Project::class, 'projects', true);

            SlugHelper::setPrefix(Property::class, 'properties', true);
            SlugHelper::setPrefix(Category::class, 'property-category', true);

            if (! setting('real_estate_disabled_public_profile')) {
                SlugHelper::registerModule(Account::class, fn () => trans('plugins/real-estate::account.agents'));
                SlugHelper::setPrefix(Account::class, 'agents', true);
                SlugHelper::setColumnUsedForSlugGenerator(Account::class, 'first_name');
            }
        });

        DashboardMenu::beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-real-estate',
                    'priority' => 0,
                    'parent_id' => null,
                    'name' => 'plugins/real-estate::real-estate.name',
                    'icon' => 'ti ti-bed',
                    'permissions' => ['plugins.real-estate'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-property',
                    'priority' => 0,
                    'parent_id' => 'cms-plugins-real-estate',
                    'name' => 'plugins/real-estate::property.name',
                    'icon' => null,
                    'url' => fn () => route('property.index'),
                    'permissions' => ['property.index'],
                ])
                ->when(RealEstateHelper::isEnabledProjects(), function (DashboardMenuSupport $dashboardMenu): void {
                    $dashboardMenu
                        ->registerItem([
                            'id' => 'cms-plugins-project',
                            'priority' => 1,
                            'parent_id' => 'cms-plugins-real-estate',
                            'name' => 'plugins/real-estate::project.name',
                            'icon' => null,
                            'url' => fn () => route('project.index'),
                            'permissions' => ['project.index'],
                        ]);
                })
                ->registerItem([
                    'id' => 'cms-plugins-re-feature',
                    'priority' => 2,
                    'parent_id' => 'cms-plugins-real-estate',
                    'name' => 'plugins/real-estate::feature.name',
                    'icon' => null,
                    'url' => fn () => route('property_feature.index'),
                    'permissions' => ['property_feature.index'],
                ])

                ->registerItem([
                    'id' => 'cms-plugins-spoken-language',
                    'priority' => 3.5,
                    'parent_id' => 'cms-plugins-real-estate',
                    'name' => 'plugins/real-estate::spoken-language.name',
                    'icon' => null,
                    'url' => fn () => route('spoken-language.index'),
                    'permissions' => ['spoken-language.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-facility',
                    'priority' => 4,
                    'parent_id' => 'cms-plugins-real-estate',
                    'name' => 'plugins/real-estate::facility.name',
                    'icon' => null,
                    'url' => fn () => route('facility.index'),
                    'permissions' => ['facility.index'],
                ])
                // ->registerItem([
                //     'id' => 'cms-plugins-investor',
                //     'priority' => 5,
                //     'parent_id' => 'cms-plugins-real-estate',
                //     'name' => 'plugins/real-estate::investor.name',
                //     'icon' => null,
                //     'url' => fn () => route('investor.index'),
                //     'permissions' => ['investor.index'],
                // ])
                // ->registerItem([
                //     'id' => 'cms-plugins-consult',
                //     'priority' => 6,
                //     'parent_id' => null,
                //     'name' => 'plugins/real-estate::consult.name',
                //     'icon' => 'ti ti-home-question',
                //     'url' => fn () => route('consult.index'),
                //     'permissions' => ['consult.index'],
                // ])
                // ->registerItem([
                //     'id' => 'cms-plugins-consult-list',
                //     'parent_id' => 'cms-plugins-consult',
                //     'name' => 'plugins/real-estate::consult.name',
                //     'route' => 'consult.index',
                //     'permissions' => ['consult.index'],
                // ])
                // ->registerItem([
                //     'id' => 'cms-plugins-consult-custom-fields',
                //     'parent_id' => 'cms-plugins-consult',
                //     'name' => 'plugins/real-estate::consult.custom_field.name',
                //     'route' => 'consult.custom-fields.index',
                //     'permissions' => ['consult.custom-fields.index'],
                // ])
                ->registerItem([
                    'id' => 'cms-plugins-real-estate-category',
                    'priority' => 4,
                    'parent_id' => 'cms-plugins-real-estate',
                    'name' => 'plugins/real-estate::category.name',
                    'icon' => null,
                    'url' => fn () => route('property_category.index'),
                    'permissions' => ['property_category.index'],
                ])
                ->when(setting('real_estate_enable_account_verification', false), function (DashboardMenuSupport $dashboardMenu): void {
                    $dashboardMenu
                        ->registerItem([
                            'id' => 'cms-plugins-real-estate-accounts',
                            'priority' => 22,
                            'parent_id' => null,
                            'name' => 'plugins/real-estate::account.name',
                            'icon' => 'ti ti-users',
                            'permissions' => ['account.index'],
                        ])
                        ->registerItem([
                            'id' => 'cms-plugins-real-estate-account',
                            'priority' => 0,
                            'parent_id' => 'cms-plugins-real-estate-accounts',
                            'name' => 'plugins/real-estate::account.name',
                            'url' => fn () => route('account.index'),
                            'permissions' => ['account.index'],
                        ])
                        ->registerItem([
                            'id' => 'cms-plugins-real-estate-unverified-accounts',
                            'priority' => 10,
                            'parent_id' => 'cms-plugins-real-estate-accounts',
                            'name' => 'plugins/real-estate::account.unverified_account.name',
                            'url' => fn () => route('unverified-accounts.index'),
                            'permissions' => ['account.index'],
                        ]);
                }, function (DashboardMenuSupport $dashboardMenu): void {
                    $dashboardMenu->registerItem([
                        'id' => 'cms-plugins-real-estate-account',
                        'priority' => 22,
                        'parent_id' => null,
                        'name' => 'plugins/real-estate::account.name',
                        'icon' => 'ti ti-users',
                        'url' => fn () => route('account.index'),
                        'permissions' => ['account.index'],
                    ]);
                })
                // ->registerItem([
                //     'id' => 'cms-plugins-real-estate-invoice',
                //     'priority' => 7,
                //     'parent_id' => 'cms-plugins-real-estate',
                //     'name' => 'plugins/real-estate::invoice.name',
                //     'url' => fn () => route('invoices.index'),
                //     'permissions' => ['invoice.index'],
                // ])
                // ->registerItem([
                //     'id' => 'cms-plugins-real-estate-coupons',
                //     'priority' => 14,
                //     'parent_id' => null,
                //     'name' => 'plugins/real-estate::coupon.name',
                //     'icon' => 'ti ti-discount-2',
                //     'url' => fn () => route('coupons.index'),
                //     'permissions' => ['real-estate.coupons.index'],
                // ])
                ->when(RealEstateHelper::isEnabledCustomFields(), function (DashboardMenuSupport $dashboardMenu): void {
                    $dashboardMenu
                        ->registerItem([
                            'id' => 'cms-plugins-real-estate-custom-fields',
                            'priority' => 13,
                            'parent_id' => 'cms-plugins-real-estate',
                            'name' => 'plugins/real-estate::custom-fields.name',
                            'icon' => null,
                            'url' => fn () => route('real-estate.custom-fields.index'),
                            'permissions' => ['real-estate.custom-fields.index'],
                        ]);
                })
                ->when(RealEstateHelper::isEnabledCreditsSystem(), function (DashboardMenuSupport $dashboardMenu): void {
                    $dashboardMenu
                        ->registerItem([
                            'id' => 'cms-plugins-package',
                            'priority' => 23,
                            'parent_id' => null,
                            'name' => 'plugins/real-estate::package.name',
                            'icon' => 'ti ti-packages',
                            'url' => fn () => route('package.index'),
                            'permissions' => ['package.index'],
                        ]);
                })
                ->when(RealEstateHelper::isEnabledReview(), function (DashboardMenuSupport $dashboardMenu): void {
                    $dashboardMenu
                        ->registerItem([
                            'id' => 'cms-plugins-real-estate-review',
                            'priority' => 5,
                            'parent_id' => 'cms-plugins-real-estate',
                            'name' => 'plugins/real-estate::review.name',
                            'icon' => null,
                            'url' => fn () => route('review.index'),
                            'permissions' => ['review.index'],
                        ]);
                });
        });

        DashboardMenu::for('account')->beforeRetrieving(function (DashboardMenuSupport $dashboardMenu): void {
            $dashboardMenu
                ->registerItem([
                    'id' => 'cms-account-dashboard',
                    'priority' => 1,
                    'name' => 'plugins/real-estate::dashboard.dashboard',
                    'url' => fn () => route('public.account.dashboard'),
                    'icon' => 'ti ti-home',
                ])
                ->when(RealEstateHelper::isEnabledCreditsSystem(), function (): void {
                    DashboardMenu::make()
                        ->registerItem([
                            'id' => 'cms-account-buy-credits',
                            'priority' => 3,
                            'name' => 'plugins/real-estate::account.buy_credits',
                            'url' => fn () => route('public.account.packages'),
                            'icon' => 'ti ti-credit-card',
                        ]);
                })
                ->registerItem([
                    'id' => 'cms-account-consult',
                    'priority' => 3,
                    'name' => 'plugins/real-estate::consult.name',
                    'url' => fn () => route('public.account.consults.index'),
                    'icon' => 'ti ti-home-question',
                ])
                ->when(RealEstateHelper::isEnabledCreditsSystem(), function (): void {
                    DashboardMenu::make()
                        ->registerItem([
                            'id' => 'cms-account-invoices',
                            'priority' => 4,
                            'name' => 'plugins/real-estate::dashboard.sidebar_invoices',
                            'url' => fn () => route('public.account.invoices.index'),
                            'icon' => 'ti ti-receipt',
                        ]);
                })
                ->registerItem([
                    'id' => 'cms-account-settings',
                    'priority' => 5,
                    'name' => 'plugins/real-estate::dashboard.header_settings_link',
                    'url' => fn () => route('public.account.settings'),
                    'icon' => 'ti ti-settings',
                ])
                ->registerItem([
                    'id' => 'cms-account-properties',
                    'priority' => 2,
                    'name' => 'plugins/real-estate::property.name',
                    'url' => fn () => route('public.account.properties.index'),
                    'icon' => 'ti ti-bed',
                ]);
        });

        DashboardMenu::default();

        PanelSectionManager::beforeRendering(function (): void {
            PanelSectionManager::default()->register(SettingRealEstatePanelSetting::class);
        });

        if (class_exists('ApiHelper')) {
            ApiHelper::setConfig([
                'model' => Account::class,
                'guard' => 'account',
                'password_broker' => 'accounts',
                'verify_email' => setting('verify_account_email', false),
            ]);
        }

        $this->app->register(CommandServiceProvider::class);

        SiteMapManager::registerKey([
            'properties-((?:19|20|21|22)\d{2})-(0?[1-9]|1[012])',
            'projects-((?:19|20|21|22)\d{2})-(0?[1-9]|1[012])',
            'property-categories',
            'agents',
            'properties-city',
            'properties-country',
            'properties-country-city',
            'projects-city',
        ]);

        if (defined('LANGUAGE_MODULE_SCREEN_NAME')) {
            if (
                defined('LANGUAGE_ADVANCED_MODULE_SCREEN_NAME') &&
                $this->app['config']->get('plugins.real-estate.real-estate.use_language_v2')
            ) {
                $this->loadRoutes(['language-advanced']);

                LanguageAdvancedManager::registerModule(Property::class, [
                    'name',
                    'description',
                    'content',
                    'location',
                ]);

                LanguageAdvancedManager::registerModule(Project::class, [
                    'name',
                    'description',
                    'content',
                    'location',
                ]);

                LanguageAdvancedManager::registerModule(Category::class, [
                    'name',
                    'description',
                ]);

                LanguageAdvancedManager::registerModule(Feature::class, [
                    'name',
                ]);

                LanguageAdvancedManager::registerModule(Facility::class, [
                    'name',
                ]);

                LanguageAdvancedManager::registerModule(Package::class, [
                    'name',
                    'description',
                    'features',
                ]);

                LanguageAdvancedManager::registerModule(CustomField::class, [
                    'name',
                    'type',
                ]);

                LanguageAdvancedManager::registerModule(CustomFieldOption::class, [
                    'label',
                    'value',
                ]);

                LanguageAdvancedManager::registerModule(CustomFieldValue::class, [
                    'name',
                    'value',
                ]);

                LanguageAdvancedManager::registerModule(Investor::class, [
                    'name',
                    'description',
                ]);

                LanguageAdvancedManager::registerModule(ConsultCustomField::class, [
                    'name',
                    'placeholder',
                ]);

                LanguageAdvancedManager::registerModule(ConsultCustomFieldOption::class, [
                    'label',
                    'value',
                ]);

                LanguageAdvancedManager::registerModule(Account::class, [
                    'first_name',
                    'last_name',
                    'description',
                ]);

                LanguageAdvancedManager::addTranslatableMetaBox('custom_fields_box');
                LanguageAdvancedManager::addTranslatableMetaBox('consult-custom-field-options');

                add_action(LANGUAGE_ADVANCED_ACTION_SAVED, function ($data, $request): void {
                    switch (get_class($data)) {
                        case Property::class:
                        case Project::class:
                            $options = $request->input('custom_fields', []) ?: [];

                            if (! $options) {
                                return;
                            }

                            foreach ($options as $value) {
                                $newRequest = new Request();

                                $newRequest->replace([
                                    'language' => $request->input('language'),
                                    'ref_lang' => Language::getRefLang(),
                                ]);

                                if (! $value['id']) {
                                    continue;
                                }

                                $optionValue = CustomFieldValue::query()->find($value['id']);

                                if ($optionValue) {
                                    $newRequest->merge([
                                        'name' => $value['name'],
                                        'value' => $value['value'],
                                    ]);

                                    LanguageAdvancedManager::save($optionValue, $newRequest);
                                }
                            }

                            break;
                        case CustomField::class:

                            $customFieldOptions = $request->input('options', []) ?: [];

                            if (! $customFieldOptions) {
                                return;
                            }

                            $newRequest = new Request();

                            $newRequest->replace([
                                'language' => $request->input('language'),
                                'ref_lang' => $request->input('ref_lang'),
                            ]);

                            foreach ($customFieldOptions as $option) {
                                if (empty($option['id'])) {
                                    continue;
                                }

                                $customFieldOption = CustomFieldOption::query()->find($option['id']);

                                if ($customFieldOption) {
                                    $newRequest->merge([
                                        'label' => $option['label'],
                                        'value' => $option['value'],
                                    ]);

                                    LanguageAdvancedManager::save($customFieldOption, $newRequest);
                                }
                            }

                            break;
                    }
                }, 1234, 2);
            } else {
                Language::registerModule([
                    Property::class,
                    Project::class,
                    Feature::class,
                    Investor::class,
                    Category::class,
                    Facility::class,
                ]);
            }
        }

        if (is_plugin_active('location')) {
            Location::registerModule(Property::class);
            Location::registerModule(Project::class);
            Location::registerModule(Account::class);
        } else {
            MacroableModels::addMacro(Property::class, 'getFullAddressAttribute', function () {
                return $this->address; // @phpstan-ignore-line
            });

            MacroableModels::addMacro(Project::class, 'getFullAddressAttribute', function () {
                return $this->address; // @phpstan-ignore-line
            });
        }

        $this->app->booted(function (): void {
            if (defined('SOCIAL_LOGIN_MODULE_SCREEN_NAME') && Route::has('public.account.login')) {
                SocialService::registerModule([
                    'guard' => 'account',
                    'model' => Account::class,
                    'login_url' => route('public.account.login'),
                    'redirect_url' => route('public.account.dashboard'),
                ]);
            }
        });

        $this->app->booted(function (): void {
            SeoHelper::registerModule([
                Property::class,
                Category::class,
                Project::class,
            ]);

            EmailHandler::addTemplateSettings(REAL_ESTATE_MODULE_SCREEN_NAME, config('plugins.real-estate.email', []));
        });

        $this->app->register(HookServiceProvider::class);
        $this->app->register(EventServiceProvider::class);

        if (is_plugin_active('rss-feed') && Route::has('feeds.properties')) {
            RssFeed::addFeedLink(route('feeds.properties'), 'Properties feed');
        }

        $this->app->afterResolving(Schedule::class, function (Schedule $schedule): void {
            $schedule
                ->command(RenewPropertiesCommand::class)
                ->dailyAt('23:30');

            // $schedule
            //     ->command('real-estate:cleanup-daily-views')
            //     ->weekly()
            //     ->sundays()
            //     ->at('02:00');
        });

        if (is_plugin_active('captcha')) {
            Captcha::registerFormSupport(LoginForm::class, LoginRequest::class, trans('plugins/real-estate::real-estate.login_form'));
            Captcha::registerFormSupport(RegisterForm::class, RegisterRequest::class, trans('plugins/real-estate::real-estate.register_form'));
            Captcha::registerFormSupport(ForgotPasswordForm::class, ForgotPasswordRequest::class, trans('plugins/real-estate::real-estate.forgot_password_form'));
            Captcha::registerFormSupport(ResetPasswordForm::class, ResetPasswordRequest::class, trans('plugins/real-estate::real-estate.reset_password_form'));
            Captcha::registerFormSupport(ConsultForm::class, SendConsultRequest::class, trans('plugins/real-estate::real-estate.consult_form'));
            Captcha::registerFormSupport(ReviewForm::class, ReviewRequest::class, trans('plugins/real-estate::real-estate.review_form'));
        }
    }

    public function setInAdmin(bool $isInAdmin): bool
    {
        $segment = request()->segment(1);

        if ($segment && in_array($segment, BaseLanguage::getLocaleKeys()) && $segment !== App::getLocale()) {
            $segment = request()->segment(2);
        }

        return $segment === 'account' || $isInAdmin;
    }
}
