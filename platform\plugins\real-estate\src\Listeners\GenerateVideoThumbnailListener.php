<?php

namespace Xmetr\RealEstate\Listeners;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Media\Events\MediaFileUploaded;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\RealEstate\Services\VideoThumbnailService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;

class GenerateVideoThumbnailListener
{
    /**
     * Handle the MediaFileUploaded event to generate thumbnails for video files
     *
     * @param MediaFileUploaded $event
     * @return void
     */
    public function handle(MediaFileUploaded $event): void
    {
        $mediaFile = $event->file;

        // Check if the uploaded file is a video
        if (!$this->isVideoFile($mediaFile->mime_type)) {
            return;
        }

        try {
            $videoThumbnailService = app(VideoThumbnailService::class);

            // Get the real path of the video file (could be local path or URL for cloud storage)
            $videoPath = RvMedia::getRealPath($mediaFile->url);

            if (!$videoPath) {
                Log::warning('Video file path could not be determined', [
                    'media_file_id' => $mediaFile->id,
                    'video_url' => $mediaFile->url,
                ]);
                return;
            }

            // For cloud storage, getRealPath returns a URL, which is fine for our updated service
            // For local storage, it returns a file path, which we need to check exists
            if (!Str::contains($videoPath, ['http://', 'https://']) && !file_exists($videoPath)) {
                Log::warning('Local video file not found for thumbnail generation', [
                    'media_file_id' => $mediaFile->id,
                    'video_url' => $mediaFile->url,
                    'video_path' => $videoPath,
                ]);
                return;
            }

            // Generate and upload thumbnail
            $result = $videoThumbnailService->generateOptimalThumbnail(
                $videoPath,
                $mediaFile->folder_id,
                null
            );

            if (!$result['error']) {
                // Store the thumbnail URL in the media file's options
                $options = $mediaFile->options ?? [];
                $options['video_thumbnail'] = $result['thumbnail_url'];

                $mediaFile->options = $options;
                $mediaFile->save();

                Log::info('Video thumbnail generated successfully', [
                    'media_file_id' => $mediaFile->id,
                    'video_url' => $mediaFile->url,
                    'thumbnail_url' => $result['thumbnail_url'],
                ]);
            } else {
                Log::warning('Failed to generate video thumbnail', [
                    'media_file_id' => $mediaFile->id,
                    'video_url' => $mediaFile->url,
                    'error' => $result['message'],
                ]);
            }

        } catch (Throwable $exception) {
            BaseHelper::logError($exception);

            Log::error('Exception occurred during video thumbnail generation', [
                'media_file_id' => $mediaFile->id,
                'video_url' => $mediaFile->url,
                'exception' => $exception->getMessage(),
            ]);
        }
    }

    /**
     * Check if the given MIME type is a video file
     *
     * @param string|null $mimeType
     * @return bool
     */
    protected function isVideoFile(?string $mimeType): bool
    {
        if (!$mimeType) {
            return false;
        }

        $videoMimeTypes = [
            'video/mp4',
            'video/mpeg',
            'video/quicktime',
            'video/x-msvideo', // AVI
            'video/x-ms-wmv',  // WMV
            'video/webm',
            'video/x-flv',     // FLV
            'video/x-matroska', // MKV
            'video/3gpp',
            'video/3gpp2',
        ];

        return in_array($mimeType, $videoMimeTypes) || Str::startsWith($mimeType, 'video/');
    }
}
